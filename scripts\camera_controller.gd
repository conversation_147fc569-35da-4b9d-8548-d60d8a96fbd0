extends Node3D
class_name CameraController

# Camera modes
enum CameraMode {
	FOLLOW,
	CINEMATIC,
	REPLAY,
	FIRST_PERSON
}

# Camera settings
@export_group("Basic Settings")
@export var mouse_sensitivity: float = 0.002
@export var distance: float = 6.0
@export var height: float = 2.0
@export var follow_speed: float = 8.0

@export_group("Zoom Settings")
@export var min_distance: float = 2.0
@export var max_distance: float = 15.0
@export var zoom_speed: float = 0.5
@export var zoom_smoothing: float = 10.0

@export_group("FOV Settings")
@export var base_fov: float = 75.0
@export var speed_fov_multiplier: float = 1.2
@export var max_speed_fov: float = 90.0

@export_group("Camera Shake")
@export var shake_intensity: float = 1.0
@export var shake_decay: float = 5.0

@export_group("Look Ahead")
@export var look_ahead_distance: float = 3.0
@export var look_ahead_speed: float = 2.0

# State
var current_mode: CameraMode = CameraMode.FOLLOW
var target_distance: float = 6.0
var yaw: float = 0.0
var pitch: float = 0.0
var min_pitch: float = -60.0
var max_pitch: float = 60.0

# Dynamic camera
var current_fov: float = 75.0
var shake_offset: Vector3 = Vector3.ZERO
var shake_timer: float = 0.0
var look_ahead_offset: Vector3 = Vector3.ZERO

# References
@onready var camera: Camera3D = $Camera3D
var target: Node3D = null
var player_controller: PlayerController = null

# Replay system
var replay_positions: Array = []
var replay_index: int = 0
var max_replay_frames: int = 300  # 5 seconds at 60fps

signal mode_changed(new_mode: CameraMode)

func _ready():
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	target_distance = distance
	current_fov = base_fov
	camera.fov = current_fov

func _input(event):
	# Camera mode switching
	if Input.is_action_just_pressed("camera_mode"):
		_cycle_camera_mode()

	# Mouse look (only in follow mode)
	if current_mode == CameraMode.FOLLOW and event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		yaw -= event.relative.x * mouse_sensitivity
		pitch += event.relative.y * mouse_sensitivity
		pitch = clamp(pitch, deg_to_rad(min_pitch), deg_to_rad(max_pitch))

	# Zoom
	if event is InputEventMouseButton and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			target_distance -= zoom_speed
			target_distance = clamp(target_distance, min_distance, max_distance)
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			target_distance += zoom_speed
			target_distance = clamp(target_distance, min_distance, max_distance)

	# Toggle mouse capture
	if Input.is_action_just_pressed("ui_cancel"):
		if Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
			Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
		else:
			Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _process(delta):
	if not target:
		return

	# Update camera based on mode
	match current_mode:
		CameraMode.FOLLOW:
			_update_follow_camera(delta)
		CameraMode.CINEMATIC:
			_update_cinematic_camera(delta)
		CameraMode.REPLAY:
			_update_replay_camera(delta)
		CameraMode.FIRST_PERSON:
			_update_first_person_camera(delta)

	# Update dynamic FOV
	_update_dynamic_fov(delta)

	# Update camera shake
	_update_camera_shake(delta)

	# Record position for replay
	_record_replay_frame()

func _update_follow_camera(delta):
	# Smooth zoom
	distance = lerp(distance, target_distance, zoom_smoothing * delta)

	# Calculate look-ahead based on velocity
	if player_controller:
		var velocity = Vector3.ZERO
		if player_controller.current_state == PlayerController.PlayerState.SKATEBOARDING and player_controller.skateboard_instance:
			velocity = player_controller.skateboard_instance.linear_velocity
		elif player_controller.current_state == PlayerController.PlayerState.WALKING:
			velocity = player_controller.velocity

		# Calculate look-ahead offset
		var target_look_ahead = velocity.normalized() * look_ahead_distance * min(velocity.length() / 10.0, 1.0)
		look_ahead_offset = look_ahead_offset.lerp(target_look_ahead, look_ahead_speed * delta)

	# Calculate camera position
	var target_pos = target.global_position + look_ahead_offset

	# Calculate offset based on yaw and pitch
	var offset = Vector3()
	offset.x = cos(pitch) * sin(yaw) * distance
	offset.y = sin(pitch) * distance + height
	offset.z = cos(pitch) * cos(yaw) * distance

	# Apply shake
	offset += shake_offset

	# Set camera position
	var desired_pos = target_pos + offset
	global_position = global_position.lerp(desired_pos, follow_speed * delta)

	# Look at target
	var look_target = target_pos + Vector3(0, height, 0)
	look_at(look_target, Vector3.UP)

func _update_cinematic_camera(delta):
	# Cinematic camera follows at a fixed angle and distance
	var target_pos = target.global_position

	# Fixed cinematic angle
	var cinematic_offset = Vector3(5, 3, 5)
	global_position = global_position.lerp(target_pos + cinematic_offset, 2.0 * delta)

	# Always look at target
	look_at(target_pos + Vector3(0, 1, 0), Vector3.UP)

func _update_replay_camera(delta):
	# Play back recorded positions
	if replay_positions.size() > 0:
		replay_index = (replay_index + 1) % replay_positions.size()
		var replay_data = replay_positions[replay_index]
		global_position = replay_data.position
		global_rotation = replay_data.rotation

func _update_first_person_camera(delta):
	# First person view from player's perspective
	if player_controller:
		global_position = player_controller.global_position + Vector3(0, 1.7, 0)

		# Use player's rotation plus mouse look
		var player_rotation = player_controller.global_rotation.y
		rotation.y = player_rotation + yaw
		rotation.x = pitch

func _update_dynamic_fov(delta):
	var target_fov = base_fov

	# Increase FOV based on speed
	if player_controller and player_controller.skateboard_instance:
		var speed = player_controller.skateboard_instance.get_speed()
		var speed_factor = min(speed / 15.0, 1.0)  # Max effect at 15 units/sec
		target_fov = base_fov + (max_speed_fov - base_fov) * speed_factor * speed_fov_multiplier

	current_fov = lerp(current_fov, target_fov, 3.0 * delta)
	camera.fov = current_fov

func _update_camera_shake(delta):
	if shake_timer > 0:
		shake_timer -= delta

		# Generate random shake offset
		var intensity = shake_timer * shake_intensity
		shake_offset = Vector3(
			randf_range(-intensity, intensity),
			randf_range(-intensity, intensity),
			randf_range(-intensity, intensity)
		)

		# Decay shake
		shake_timer = max(0, shake_timer - shake_decay * delta)
	else:
		shake_offset = Vector3.ZERO

func _record_replay_frame():
	var frame_data = {
		"position": global_position,
		"rotation": global_rotation
	}

	replay_positions.append(frame_data)

	# Keep only recent frames
	if replay_positions.size() > max_replay_frames:
		replay_positions.pop_front()

func _cycle_camera_mode():
	match current_mode:
		CameraMode.FOLLOW:
			current_mode = CameraMode.CINEMATIC
		CameraMode.CINEMATIC:
			current_mode = CameraMode.REPLAY
		CameraMode.REPLAY:
			current_mode = CameraMode.FIRST_PERSON
		CameraMode.FIRST_PERSON:
			current_mode = CameraMode.FOLLOW

	mode_changed.emit(current_mode)

# Public interface
func set_target(new_target: Node3D):
	target = new_target

func set_player_controller(controller: PlayerController):
	player_controller = controller

func add_shake(intensity: float, duration: float):
	shake_timer = max(shake_timer, duration)
	shake_intensity = intensity

func set_mode(mode: CameraMode):
	current_mode = mode
	mode_changed.emit(current_mode)


