extends Node3D

# References to main components
@onready var player = $Player
@onready var camera_controller = $CameraController
@onready var environment = $Environment

# Skateboard scene reference
@export var skateboard_scene: PackedScene

func _ready():
	# Set up the skateboard scene reference for the player
	if skateboard_scene:
		player.skateboard_scene = skateboard_scene
	else:
		# Load the skateboard scene
		skateboard_scene = load("res://scenes/skateboard.tscn")
		player.skateboard_scene = skateboard_scene
	
	# Set up camera to follow player
	camera_controller.set_target(player)

	# Give player reference to camera for relative movement
	player.camera_controller = camera_controller
	
	# Position player at spawn point
	player.global_position = environment.get_spawn_position()
	
	# Set up lighting
	_setup_lighting()

func _setup_lighting():
	var light = $DirectionalLight3D
	light.position = Vector3(0, 10, 5)
	light.rotation_degrees = Vector3(-45, -30, 0)
	light.light_energy = 1.0
	light.shadow_enabled = true

func _input(_event):
	# Handle any global input here if needed
	pass
