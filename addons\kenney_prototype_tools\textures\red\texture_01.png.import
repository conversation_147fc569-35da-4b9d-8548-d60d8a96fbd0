[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ntiu66svity3"
path.s3tc="res://.godot/imported/texture_01.png-031f16b1234aadc0b23c0e7873db99a9.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://addons/kenney_prototype_tools/textures/red/texture_01.png"
dest_files=["res://.godot/imported/texture_01.png-031f16b1234aadc0b23c0e7873db99a9.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/uastc_level=0
compress/rdo_quality_loss=0.0
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/channel_remap/red=0
process/channel_remap/green=1
process/channel_remap/blue=2
process/channel_remap/alpha=3
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
