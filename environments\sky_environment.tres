[gd_resource type="Environment" load_steps=3 format=3 uid="uid://bqxvn8ywqxqxq"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_1"]
sky_top_color = Color(0.385, 0.647, 0.937, 1)
sky_horizon_color = Color(0.646, 0.656, 0.67, 1)
ground_bottom_color = Color(0.156, 0.184, 0.211, 1)
ground_horizon_color = Color(0.423, 0.396, 0.372, 1)
sun_angle_max = 30.0
sun_curve = 0.15

[sub_resource type="Sky" id="Sky_1"]
sky_material = SubResource("ProceduralSkyMaterial_1")

[resource]
background_mode = 2
sky = SubResource("Sky_1")
ambient_light_source = 3
ambient_light_color = Color(0.8, 0.9, 1, 1)
ambient_light_energy = 0.3
reflected_light_source = 2
tonemap_mode = 2
tonemap_exposure = 1.0
tonemap_white = 1.0
ssr_enabled = true
ssao_enabled = true
ssil_enabled = true
sdfgi_enabled = false
glow_enabled = true
glow_levels/1 = 0.0
glow_levels/2 = 0.0
glow_levels/3 = 1.0
glow_levels/4 = 0.0
glow_levels/5 = 1.0
glow_levels/6 = 0.0
glow_levels/7 = 0.0
glow_normalized = true
glow_intensity = 0.8
glow_strength = 1.0
glow_mix = 0.05
glow_bloom = 0.0
glow_blend_mode = 2
glow_hdr_threshold = 1.0
glow_hdr_scale = 2.0
glow_hdr_luminance_cap = 12.0
glow_map_strength = 0.0
fog_enabled = false
adjustment_enabled = false
