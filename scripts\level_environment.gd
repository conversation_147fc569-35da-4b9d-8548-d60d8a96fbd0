extends Node3D
class_name LevelEnvironment

# Level properties
@export var ground_size: Vector2 = Vector2(100, 100)
@export var ground_height: float = 0.1

# References
@onready var ground_mesh: MeshInstance3D = $Ground/MeshInstance3D
@onready var ground_collision: StaticBody3D = $Ground
@onready var ground_shape: CollisionShape3D = $Ground/CollisionShape3D

func _ready():
	# Environment elements will be set up in the scene editor
	# Add to ground group for skateboard detection
	if ground_collision:
		ground_collision.add_to_group("ground")

# Environment objects will be set up in the scene editor

func get_spawn_position() -> Vector3:
	return Vector3(0, 1, 0)

func is_within_bounds(pos: Vector3) -> bool:
	return abs(pos.x) < ground_size.x/2 and abs(pos.z) < ground_size.y/2
