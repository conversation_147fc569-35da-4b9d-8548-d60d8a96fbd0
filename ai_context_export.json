{"autoloads": {}, "generated_timestamp": "2025-07-28T23:54:09", "project_name": "skate", "project_settings": {"application": {"description": "", "main_scene": "res://scenes/main.tscn", "name": "skate"}, "display": {"height": 648, "width": 1152}, "input": {"map": {"spatial_editor/freelook_backwards": ["InputEventKey: keycode=83 (S), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_down": ["InputEventKey: keycode=81 (Q), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_forward": ["InputEventKey: keycode=87 (W), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_left": ["InputEventKey: keycode=65 (A), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_right": ["InputEventKey: keycode=68 (D), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_slow_modifier": ["InputEventKey: keycode=4194328 (Alt), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_speed_modifier": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_up": ["InputEventKey: keycode=69 (E), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_orbit_modifier_1": [], "spatial_editor/viewport_orbit_modifier_2": [], "spatial_editor/viewport_pan_modifier_1": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_pan_modifier_2": [], "spatial_editor/viewport_zoom_modifier_1": ["InputEventKey: keycode=4194326 (Ctrl), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_zoom_modifier_2": [], "ui_accept": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_accessibility_drag_and_drop": [], "ui_cancel": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_colorpicker_delete_preset": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_copy": ["InputEventKey: keycode=67 (C), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_cut": ["InputEventKey: keycode=88 (X), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194312 (Delete), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_refresh": ["InputEventKey: keycode=4194336 (F5), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_show_hidden": ["InputEventKey: keycode=72 (H), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_up_one_level": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_mode": ["InputEventKey: keycode=77 (M), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_next": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_prev": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_duplicate": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_follow_left": ["InputEventKey: keycode=4194319 (Left), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_follow_right": ["InputEventKey: keycode=4194321 (Right), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_home": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_menu": ["InputEventKey: keycode=4194370 (Menu), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_paste": ["InputEventKey: keycode=86 (V), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_redo": ["InputEventKey: keycode=90 (Z), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=89 (Y), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_select": ["InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_swap_input_direction": ["InputEventKey: keycode=96 (QuoteLeft), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_add_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194308 (Backspace), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace_all_to_left": [], "ui_text_backspace_word": ["InputEventKey: keycode=4194308 (Backspace), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_above": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_below": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_end": ["InputEventKey: keycode=4194318 (End), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_start": ["InputEventKey: keycode=4194317 (Home), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_start": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_left": ["InputEventKey: keycode=4194319 (Left), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_right": ["InputEventKey: keycode=4194321 (Right), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_clear_carets_and_selection": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_accept": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_query": ["InputEventKey: keycode=32 (Space), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_replace": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_dedent": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete_all_to_right": [], "ui_text_delete_word": ["InputEventKey: keycode=4194312 (Delete), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_indent": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_above": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_blank": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_down": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_up": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_all": ["InputEventKey: keycode=65 (A), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_word_under_caret": ["InputEventKey: keycode=71 (G), mods=Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_skip_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl+Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_submit": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_toggle_insert_mode": ["InputEventKey: keycode=4194311 (Insert), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_undo": ["InputEventKey: keycode=90 (Z), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_unicode_start": ["InputEventKey: keycode=85 (U), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"]}}, "physics": {}, "rendering": {}}, "scenes": [{"path": "res:///addons/kenney_prototype_tools/cube.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"mesh": "res://addons/kenney_prototype_tools/cube.tscn::BoxMesh_rgswm", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/dark/dark_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/dark/dark_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/green/green_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/green/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/green/green_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/light/light_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/light/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/light/light_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/orange/orange_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/orange/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/orange/orange_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/purple/purple_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/purple/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/purple/purple_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_01.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_01.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_01.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_02.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_02.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_02.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_03.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_03.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_03.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_04.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_04.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_04.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_05.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_05.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_05.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_06.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_06.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_06.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_07.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_07.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_07.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_08.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_08.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_08.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_09.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_09.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_09.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_10.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_10.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_10.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_11.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_11.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_11.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_12.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_12.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_12.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/scenes/red/red_13.tscn", "root_node": {"children": [{"children": [], "groups": [], "name": "<PERSON><PERSON>", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/red/material_13.tres", "mesh": "res://addons/kenney_prototype_tools/scenes/red/red_13.tscn::BoxMesh_orukj", "position": [0.5, 0.5, 0.5], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "C<PERSON>", "properties": {}, "script": "", "type": "Node3D"}}, {"path": "res:///addons/kenney_prototype_tools/tool_gen.tscn", "root_node": {"children": [], "groups": [], "name": "ToolGen", "properties": {}, "script": "", "type": "Node"}}, {"path": "res:///scenes/main.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "output", "properties": {"mesh": "res://assets/models/skater/skater.glb::A<PERSON><PERSON><PERSON><PERSON>_arjlb", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "SkaterModel", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "MeshInstance3D", "properties": {}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, -0.100000023841858, 0.0], "shape": "res://scenes/main.tscn::CapsuleShape3D_1"}, "script": "", "type": "CollisionShape3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "SkateboardPickupArea", "properties": {}, "script": "", "type": "Area3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_feet"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "DeckDetectionArea", "properties": {"position": [0.0, -0.899999976158142, 0.0]}, "script": "", "type": "Area3D"}], "groups": [], "name": "Player", "properties": {"air_control": 0.3, "bail_threshold_angle": 45.0, "bail_threshold_speed": 18.0, "mount_animation_time": 0.3, "position": [0.0, 1.0, 0.0], "rotation": [0.0, 3.14159250259399, 0.0], "script": "res://scripts/player_controller.gd", "skateboard_scene": "res://scenes/skateboard.tscn"}, "script": "res://scripts/player_controller.gd", "type": "CharacterBody3D"}, {"children": [{"children": [], "groups": [], "name": "Camera3D", "properties": {"rotation": [-0.523599028587341, 0.0, 0.0]}, "script": "", "type": "Camera3D"}, {"children": [], "groups": [], "name": "CameraPivot", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "CameraController", "properties": {"base_fov": 75.0, "distance": 6.0, "follow_speed": 8.0, "height": 2.0, "look_ahead_distance": 3.0, "look_ahead_speed": 2.0, "max_distance": 15.0, "max_speed_fov": 90.0, "min_distance": 2.0, "mouse_sensitivity": 0.002, "position": [0.0, 2.0, 5.0], "script": "res://scripts/camera_controller.gd", "shake_decay": 5.0, "shake_intensity": 1.0, "speed_fov_multiplier": 1.2, "zoom_smoothing": 10.0, "zoom_speed": 0.5}, "script": "res://scripts/camera_controller.gd", "type": "Node3D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://addons/kenney_prototype_tools/materials/dark/material_09.tres", "mesh": "res://scenes/main.tscn::BoxMesh_1", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_1"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "Ground", "properties": {"position": [0.0, -0.0500000007450581, 0.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "CSGBox3D", "properties": {"material": "res://addons/kenney_prototype_tools/materials/red/material_04.tres", "position": [0.0, 2.0, -3.09999871253967], "size": [10.0, 4.0, 2.0]}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "CSGCylinder3D", "properties": {"height": 4.03369140625, "material": "res://scenes/main.tscn::StandardMaterial3D_tefeu", "operation": 2, "position": [0.0, 2.016845703125, 1.9073486328125e-06], "radius": 4.0, "sides": 32}, "script": "", "type": "CSGCylinder3D"}], "groups": [], "name": "QuarterPipe1", "properties": {"position": [0.0, 4.09999990463257, -15.0], "rotation": [-1.57079637050629, 0.0, 0.0], "use_collision": true}, "script": "", "type": "CSGCombiner3D"}, {"children": [{"children": [], "groups": [], "name": "CSGBox3D", "properties": {"material": "res://addons/kenney_prototype_tools/materials/purple/material_04.tres", "position": [0.0, 1.0, -2.0], "rotation": [-1.57079637050629, -8.74227907843306e-08, 0.0], "size": [10.0, 2.0, 4.0]}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "CSGCylinder3D", "properties": {"height": 4.269775390625, "material": "res://addons/kenney_prototype_tools/materials/purple/material_04.tres", "operation": 2, "position": [-8.74226486757834e-08, 0.9046630859375, 0.999999761581421], "radius": 4.0, "sides": 32}, "script": "", "type": "CSGCylinder3D"}], "groups": [], "name": "QuarterPipe2", "properties": {"position": [0.0, 3.0, 15.0], "rotation": [-1.57079637050629, -3.14159274101257, 0.0], "use_collision": true}, "script": "", "type": "CSGCombiner3D"}, {"children": [{"children": [], "groups": [], "name": "Ramp1", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 0.485285967588425, -4.688889503479], "rotation": [-0.261799603700638, 0.0, 0.0], "size": [4.0, 1.0, 7.75], "use_collision": true}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "Box", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 0.962500035762787, 0.0], "size": [1.0, 1.92500007152557, 0.5], "use_collision": true}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "Ramp2", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 0.485285967588425, 4.688889503479], "rotation": [0.261799603700638, 0.0, 0.0], "size": [4.0, 1.0, 7.75], "use_collision": true}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "Rail", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 2.02500009536743, 0.0], "size": [0.200000002980232, 0.200000002980232, 2.72500014305115], "use_collision": true}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "CSGBox3D", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 0.5106201171875, 2.69110345840454], "size": [4.0, 1.02924799919128, 3.74591827392578], "use_collision": true}, "script": "", "type": "CSGBox3D"}, {"children": [], "groups": [], "name": "CSGBox3D2", "properties": {"material": "res://addons/kenney_prototype_tools/materials/orange/material_04.tres", "position": [0.0, 0.5106201171875, -2.70064258575439], "size": [4.0, 1.02924799919128, 3.75628662109375], "use_collision": true}, "script": "", "type": "CSGBox3D"}], "groups": [], "name": "Funbox", "properties": {"position": [-10.0, 0.0, 0.0]}, "script": "", "type": "Node3D"}], "groups": [], "name": "Skatepark", "properties": {}, "script": "", "type": "Node3D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_4", "mesh": "res://scenes/main.tscn::BoxMesh_3", "position": [0.0, 0.0, 10.0], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, 0.0, 10.0], "shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "RedCube", "properties": {"position": [10.0, 0.5, 10.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_5", "mesh": "res://scenes/main.tscn::BoxMesh_3", "position": [0.0, 0.0, 10.0], "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, 0.0, 10.0], "shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "GreenCube", "properties": {"position": [-10.0, 0.5, 10.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_6", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "BlueCube", "properties": {"position": [10.0, 0.5, -20.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_7", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "YellowCube", "properties": {"position": [-10.0, 0.5, -20.0]}, "script": "", "type": "StaticBody3D"}], "groups": [], "name": "ReferenceCubes", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Environment", "properties": {"ground_height": 0.1, "ground_size": [100.0, 100.0], "script": "res://scripts/level_environment.gd"}, "script": "res://scripts/level_environment.gd", "type": "Node3D"}, {"children": [], "groups": [], "name": "DirectionalLight3D", "properties": {"position": [0.0, 10.0, 5.0], "rotation": [-0.785398066043854, 0.785398185253143, 0.0], "shadow_enabled": true}, "script": "", "type": "DirectionalLight3D"}], "groups": [], "name": "Main", "properties": {"script": "res://scripts/main.gd"}, "script": "res://scripts/main.gd", "type": "Node3D"}}, {"path": "res:///scenes/skateboard.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "Skateboard_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>y<PERSON><PERSON>_wrj8j", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard_1", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON><PERSON><PERSON><PERSON>_wyxst", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [{"children": [], "groups": [], "name": "Skateboard2_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::<PERSON><PERSON><PERSON><PERSON><PERSON>_pouom", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "Skateboard2", "properties": {}, "script": "", "type": "Node3D"}, {"children": [{"children": [], "groups": [], "name": "Skateboard1_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>y<PERSON><PERSON>_kefqw", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_02", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_ys1ok", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_1", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::ArrayMesh_o0tmw", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_2", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_b5vg4", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_22", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_rkbod", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "Skateboard1", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Skateboard", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Root", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Sketchfab_model", "properties": {"rotation": [-1.57079637050629, 0.0, 0.0]}, "script": "", "type": "Node3D"}], "groups": [], "name": "SkateboardModel", "properties": {"position": [0.0, 0.0377334654331207, 0.0], "scale": [0.174999997019768, 0.174999997019768, 0.174999997019768]}, "script": "", "type": "Node3D"}], "groups": [], "name": "MeshInstance3D", "properties": {}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, 0.0750000029802322, 0.0], "shape": "res://scenes/skateboard.tscn::BoxShape3D_1"}, "script": "", "type": "CollisionShape3D"}, {"children": [], "groups": [], "name": "FrontWheels", "properties": {"position": [0.0, -0.0500000007450581, 0.400000005960464]}, "script": "", "type": "Node3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [-0.00161132216453552, 0.136011108756065, 0.000266149640083313], "shape": "res://scenes/skateboard.tscn::BoxShape3D_deck"}, "script": "", "type": "CollisionShape3D"}, {"children": [], "groups": [], "name": "CollisionShape3D2", "properties": {"position": [0.661039471626282, 0.169069215655327, 0.000266149640083313], "rotation": [0.0, 0.0, 0.349065870046616], "shape": "res://scenes/skateboard.tscn::BoxShape3D_ma3gn"}, "script": "", "type": "CollisionShape3D"}, {"children": [], "groups": [], "name": "CollisionShape3D3", "properties": {"position": [-0.657270014286041, 0.172694444656372, 0.000266149640083313], "rotation": [0.0, 0.0, 2.70526027679443], "shape": "res://scenes/skateboard.tscn::BoxShape3D_ma3gn"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "DeckSurface", "properties": {}, "script": "", "type": "Area3D"}, {"children": [], "groups": [], "name": "BackWheels", "properties": {"position": [0.0, -0.0500000007450581, -0.400000005960464]}, "script": "", "type": "Node3D"}, {"children": [], "groups": [], "name": "GroundRay", "properties": {"position": [0.0, 0.152361631393433, 0.0], "target_position": [0.0, -0.25, 0.0]}, "script": "", "type": "RayCast3D"}, {"children": [], "groups": [], "name": "GroundRayFrontLeft", "properties": {"position": [-0.300000011920929, 0.150000005960464, 0.400000005960464], "target_position": [0.0, -0.5, 0.0]}, "script": "", "type": "RayCast3D"}, {"children": [], "groups": [], "name": "GroundRayFrontRight", "properties": {"position": [0.300000011920929, 0.150000005960464, 0.400000005960464], "target_position": [0.0, -0.5, 0.0]}, "script": "", "type": "RayCast3D"}, {"children": [], "groups": [], "name": "GroundRayBackLeft", "properties": {"position": [-0.300000011920929, 0.150000005960464, -0.400000005960464], "target_position": [0.0, -0.5, 0.0]}, "script": "", "type": "RayCast3D"}, {"children": [], "groups": [], "name": "GroundRayBackRight", "properties": {"position": [0.300000011920929, 0.150000005960464, -0.400000005960464], "target_position": [0.0, -0.5, 0.0]}, "script": "", "type": "RayCast3D"}], "groups": ["skateboard"], "name": "Skateboard", "properties": {"acceleration_force": 25.0, "angular_damp": 1.0, "bearing_friction": 0.98, "board_flex": 0.15, "brake_force": 15.0, "carve_multiplier": 1.5, "contact_monitor": true, "continuous_cd": true, "flip_speed": 720.0, "grind_balance_speed": 2.0, "landing_compression": 0.3, "lean_angle_max": 35.0, "lean_speed": 3.0, "linear_damp": 0.5, "manual_balance_difficulty": 1.5, "mass": 2.0, "max_contacts_reported": 10, "max_speed": 20.0, "momentum_preservation": 0.97, "ollie_force": 12.0, "ollie_torque": 5.0, "powerslide_friction": 0.7, "rolling_resistance": 0.02, "script": "res://scripts/skateboard.gd", "speed_wobble_intensity": 0.1, "speed_wobble_threshold": 15.0, "surface_friction_multiplier": 1.0, "suspension_damping": 15.0, "suspension_stiffness": 150.0, "truck_looseness": 0.8, "turn_rate": 2.5, "weight_distribution": 0.6, "wheel_radius": 0.03}, "script": "res://scripts/skateboard.gd", "type": "RigidBody3D"}}], "scripts": [{"content": "@tool\nextends RefCounted\n\nfunc export_project_structure() -> bool:\n\tvar output = {\n\t\t\"project_name\": ProjectSettings.get_setting(\"application/config/name\", \"Unknown\"),\n\t\t\"generated_timestamp\": Time.get_datetime_string_from_system(),\n\t\t\"scenes\": [],\n\t\t\"scripts\": [],\n\t\t\"autoloads\": {},\n\t\t\"project_settings\": get_relevant_project_settings()\n\t}\n\t\n\t# Get all scene files\n\tvar scene_files = find_files_recursive(\"res://\", \"tscn\")\n\tvar total_files = scene_files.size()\n\t\n\tprint(\"AI Context Generator: Found %d scenes to process...\" % total_files)\n\t\n\t# Process each scene\n\tfor i in range(scene_files.size()):\n\t\tvar scene_path = scene_files[i]\n\t\t\n\t\tprint(\"AI Context Generator: Processing scene %d/%d: %s\" % [i+1, total_files, scene_path.get_file()])\n\t\t\n\t\tvar scene_data = parse_scene_structure(scene_path)\n\t\tif scene_data:\n\t\t\toutput.scenes.append(scene_data)\n\t\n\t# Get all script files\n\tvar script_files = find_files_recursive(\"res://\", \"gd\")\n\tscript_files.append_array(find_files_recursive(\"res://\", \"cs\"))\n\t\n\tprint(\"AI Context Generator: Processing %d scripts...\" % script_files.size())\n\t\n\t# Process each script\n\tfor i in range(script_files.size()):\n\t\tvar script_path = script_files[i]\n\t\t\n\t\tvar script_data = extract_script_info(script_path)\n\t\tif script_data:\n\t\t\toutput.scripts.append(script_data)\n\t\n\t# Get autoloads\n\toutput.autoloads = get_autoloads()\n\t\n\t# Save to file\n\tvar json_string = JSON.stringify(output, \"\\t\")\n\tvar file = FileAccess.open(\"res://ai_context_export.json\", FileAccess.WRITE)\n\t\n\tif file:\n\t\tfile.store_string(json_string)\n\t\tfile.close()\n\t\t\n\t\t# Copy to clipboard\n\t\tDisplayServer.clipboard_set(json_string)\n\t\t\n\t\tprint(\"AI Context Generator: Successfully generated ai_context_export.json\")\n\t\tprint(\"AI Context Generator: JSON data copied to clipboard!\")\n\t\treturn true\n\telse:\n\t\tprint(\"AI Context Generator: Failed to write export file\")\n\t\treturn false\n\nfunc find_files_recursive(path: String, extension: String) -> Array[String]:\n\tvar files: Array[String] = []\n\tvar dir = DirAccess.open(path)\n\t\n\tif dir:\n\t\tdir.list_dir_begin()\n\t\tvar file_name = dir.get_next()\n\t\t\n\t\twhile file_name != \"\":\n\t\t\tvar full_path = path + \"/\" + file_name\n\t\t\t\n\t\t\tif dir.current_is_dir() and not file_name.begins_with(\".\"):\n\t\t\t\tfiles.append_array(find_files_recursive(full_path, extension))\n\t\t\telif file_name.get_extension() == extension:\n\t\t\t\tfiles.append(full_path)\n\t\t\t\n\t\t\tfile_name = dir.get_next()\n\t\t\n\t\tdir.list_dir_end()\n\t\n\treturn files\n\nfunc parse_scene_structure(scene_path: String) -> Dictionary:\n\tvar scene = load(scene_path) as PackedScene\n\tif not scene:\n\t\treturn {}\n\t\n\tvar instance = scene.instantiate()\n\tif not instance:\n\t\treturn {}\n\t\n\tvar scene_data = {\n\t\t\"path\": scene_path,\n\t\t\"root_node\": parse_node_recursive(instance)\n\t}\n\t\n\tinstance.queue_free()\n\treturn scene_data\n\nfunc parse_node_recursive(node: Node) -> Dictionary:\n\tvar node_data = {\n\t\t\"name\": node.name,\n\t\t\"type\": node.get_class(),\n\t\t\"script\": \"\",\n\t\t\"properties\": {},\n\t\t\"groups\": [],\n\t\t\"children\": []\n\t}\n\t\n\t# Get script path if attached\n\tif node.get_script():\n\t\tnode_data.script = node.get_script().resource_path\n\t\n\t# Get non-default properties\n\tnode_data.properties = get_non_default_properties(node)\n\t\n\t# Get groups\n\tfor group in node.get_groups():\n\t\tnode_data.groups.append(group)\n\t\n\t# Process children\n\tfor child in node.get_children():\n\t\tnode_data.children.append(parse_node_recursive(child))\n\t\n\treturn node_data\n\nfunc get_non_default_properties(node: Node) -> Dictionary:\n\tvar properties = {}\n\t\n\t# Create a default instance of the same type for comparison\n\tvar default_node = null\n\tvar node_class = node.get_class()\n\t\n\t# Try to create default instance\n\tif ClassDB.can_instantiate(node_class):\n\t\tdefault_node = ClassDB.instantiate(node_class)\n\t\n\tif not default_node:\n\t\treturn properties\n\t\n\t# Get all properties\n\tvar property_list = node.get_property_list()\n\t\n\tfor prop in property_list:\n\t\t# Only include editor-visible properties that aren't built-in engine properties\n\t\tif (prop.usage & PROPERTY_USAGE_EDITOR) and not prop.name.begins_with(\"_\"):\n\t\t\tvar current_value = node.get(prop.name)\n\t\t\tvar default_value = default_node.get(prop.name)\n\t\t\t\n\t\t\t# Compare values (handle different types appropriately)\n\t\t\tif not values_equal(current_value, default_value):\n\t\t\t\tproperties[prop.name] = serialize_value(current_value)\n\t\n\tdefault_node.queue_free()\n\treturn properties\n\nfunc values_equal(a, b) -> bool:\n\t# Handle Vector2, Vector3, Color, etc.\n\tif typeof(a) != typeof(b):\n\t\treturn false\n\t\n\tif a is Vector2 or a is Vector3 or a is Vector4:\n\t\treturn a.is_equal_approx(b)\n\telif a is Color:\n\t\treturn a.is_equal_approx(b)\n\telse:\n\t\treturn a == b\n\nfunc serialize_value(value):\n\t# Convert complex types to serializable formats\n\tif value is Vector2:\n\t\treturn [value.x, value.y]\n\telif value is Vector3:\n\t\treturn [value.x, value.y, value.z]\n\telif value is Vector4:\n\t\treturn [value.x, value.y, value.z, value.w]\n\telif value is Color:\n\t\treturn [value.r, value.g, value.b, value.a]\n\telif value is Resource and value.resource_path != \"\":\n\t\treturn value.resource_path\n\telse:\n\t\treturn value\n\nfunc extract_script_info(script_path: String) -> Dictionary:\n\tvar file = FileAccess.open(script_path, FileAccess.READ)\n\tif not file:\n\t\treturn {}\n\t\n\tvar content = file.get_as_text()\n\tfile.close()\n\t\n\tvar script_data = {\n\t\t\"path\": script_path,\n\t\t\"language\": script_path.get_extension(),\n\t\t\"content\": content,\n\t\t\"exports\": extract_exported_variables(content),\n\t\t\"signals\": extract_signals(content),\n\t\t\"functions\": extract_functions(content)\n\t}\n\t\n\treturn script_data\n\nfunc extract_exported_variables(content: String) -> Array:\n\tvar exports = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"@export\"):\n\t\t\texports.append(line)\n\t\n\treturn exports\n\nfunc extract_signals(content: String) -> Array:\n\tvar signals = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"signal \"):\n\t\t\tsignals.append(line)\n\t\n\treturn signals\n\nfunc extract_functions(content: String) -> Array:\n\tvar functions = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"func \"):\n\t\t\tfunctions.append(line)\n\t\n\treturn functions\n\nfunc get_autoloads() -> Dictionary:\n\tvar autoloads = {}\n\t\n\t# Get autoload settings from project settings\n\tvar settings = ProjectSettings.get_property_list()\n\t\n\tfor setting in settings:\n\t\tif setting.name.begins_with(\"autoload/\"):\n\t\t\tvar autoload_name = setting.name.substr(9)  # Remove \"autoload/\" prefix\n\t\t\tvar autoload_path = ProjectSettings.get_setting(setting.name)\n\t\t\tautoloads[autoload_name] = autoload_path\n\t\n\treturn autoloads\n\nfunc get_relevant_project_settings() -> Dictionary:\n\tvar settings = {\n\t\t\"application\": {},\n\t\t\"display\": {},\n\t\t\"input\": {},\n\t\t\"physics\": {},\n\t\t\"rendering\": {}\n\t}\n\t\n\t# Application settings\n\tsettings.application[\"name\"] = ProjectSettings.get_setting(\"application/config/name\", \"\")\n\tsettings.application[\"description\"] = ProjectSettings.get_setting(\"application/config/description\", \"\")\n\tsettings.application[\"main_scene\"] = ProjectSettings.get_setting(\"application/run/main_scene\", \"\")\n\t\n\t# Display settings\n\tsettings.display[\"width\"] = ProjectSettings.get_setting(\"display/window/size/viewport_width\", 0)\n\tsettings.display[\"height\"] = ProjectSettings.get_setting(\"display/window/size/viewport_height\", 0)\n\t\n\t# Input map\n\tvar input_map = {}\n\tfor action in InputMap.get_actions():\n\t\tinput_map[action] = []\n\t\tfor event in InputMap.action_get_events(action):\n\t\t\tinput_map[action].append(str(event))\n\tsettings.input[\"map\"] = input_map\n\t\n\treturn settings\n", "exports": [], "functions": ["func export_project_structure() -> bool:", "func find_files_recursive(path: String, extension: String) -> Array[String]:", "func parse_scene_structure(scene_path: String) -> Dictionary:", "func parse_node_recursive(node: Node) -> Dictionary:", "func get_non_default_properties(node: Node) -> Dictionary:", "func values_equal(a, b) -> bool:", "func serialize_value(value):", "func extract_script_info(script_path: String) -> Dictionary:", "func extract_exported_variables(content: String) -> Array:", "func extract_signals(content: String) -> Array:", "func extract_functions(content: String) -> Array:", "func get_autoloads() -> Dictionary:", "func get_relevant_project_settings() -> Dictionary:"], "language": "gd", "path": "res:///addons/ai_context_generator/ai_context_generator.gd", "signals": []}, {"content": "@tool\nextends EditorPlugin\n\nconst AIContextGenerator = preload(\"res://addons/ai_context_generator/ai_context_generator.gd\")\n\nfunc _enter_tree():\n\t# Add a simple menu item to the Project menu\n\tadd_tool_menu_item(\"Generate AI Context\", _on_generate_context)\n\nfunc _exit_tree():\n\t# Clean up\n\tremove_tool_menu_item(\"Generate AI Context\")\n\nfunc _on_generate_context():\n\tvar generator = AIContextGenerator.new()\n\tgenerator.export_project_structure()\n", "exports": [], "functions": ["func _enter_tree():", "func _exit_tree():", "func _on_generate_context():"], "language": "gd", "path": "res:///addons/ai_context_generator/plugin.gd", "signals": []}, {"content": "# Editor script to generate the materials and cube scenes using the Kenney textures in the textures folder\nextends Node\n\n@tool\n\n# Functions as a button to regenerate the materials and scenes\n@export var generate := false\n\nconst textures_path := \"res://addons/kenney_prototype_tools/textures\"\nconst materials_path := \"res://addons/kenney_prototype_tools/materials/\"\nconst scenes_path := \"res://addons/kenney_prototype_tools/scenes/\"\n\nconst cube_scene = preload(\"res://addons/tools/cube.tscn\")\n\n# Generates the material and the scene corresponding to this color and texture\nfunc generate_tex(col: String, tex_name: String):\n\tvar id =  tex_name.trim_prefix(\"texture_\").trim_suffix(\".png\")\n\n\t# Create the material and save it\n\tvar mat := StandardMaterial3D.new()\n\tmat.albedo_texture = load(textures_path.path_join(col).path_join(tex_name))\n\tmat.vertex_color_is_srgb = true\n\tmat.uv1_triplanar = true\n\tmat.uv1_world_triplanar = true\n\tmat.texture_filter = BaseMaterial3D.TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC\n\tvar mat_name = \"material_\" + id + \".tres\"\n\tvar mat_path = materials_path.path_join(col).path_join(mat_name)\n\tResourceSaver.save(mat, mat_path)\n\tprint(\"Material \", col, \" \", id)\n\n\t# Create the scene and save it\n\tvar cube = cube_scene.instantiate()\n\tcube.get_node(\"Mesh\").material_override = load(mat_path)\n\tvar new_cube_scene = PackedScene.new()\n\tnew_cube_scene.pack(cube)\n\tvar scene_name = col + \"_\" + id + \".tscn\"\n\tvar scene_path = scenes_path.path_join(col).path_join(scene_name)\n\tResourceSaver.save(new_cube_scene, scene_path)\n\tprint(\"Scene \", col, \" \", id, \" \", scene_path)\n\n# Generates materials and scenes for all colors and textures\nfunc generate_tools():\n\t# Iterate over all colors in textures\n\tfor col in DirAccess.open(textures_path).get_directories():\n\t\t# Creates materials and scenes parent folders\n\t\tDirAccess.make_dir_recursive_absolute(materials_path.path_join(col))\n\t\tDirAccess.make_dir_recursive_absolute(scenes_path.path_join(col))\n\t\t# Get texture files of current color\n\t\tvar tex_col_path = textures_path.path_join(col)\n\t\tvar files = Array(DirAccess.open(tex_col_path).get_files())\n\t\t# Ignore .import files generated by Godot\n\t\tfiles = files.filter(func(s): return !s.ends_with(\".import\"))\n\t\t# Iterate over all texture files\n\t\tfor tex in files:\n\t\t\tgenerate_tex(col, tex)\n\nfunc _process(_delta):\n\tif generate:\n\t\tgenerate = false\n\t\tgenerate_tools()\n", "exports": ["@export var generate := false"], "functions": ["func generate_tex(col: String, tex_name: String):", "func generate_tools():", "func _process(_delta):"], "language": "gd", "path": "res:///addons/kenney_prototype_tools/tool_generator.gd", "signals": []}, {"content": "extends Node3D\nclass_name CameraController\n\n# Camera modes\nenum CameraMode {\n\tFOLLOW,\n\tCINEMATIC,\n\tREPLAY,\n\tFIRST_PERSON\n}\n\n# Camera settings\n@export_group(\"Basic Settings\")\n@export var mouse_sensitivity: float = 0.002\n@export var distance: float = 6.0\n@export var height: float = 2.0\n@export var follow_speed: float = 8.0\n\n@export_group(\"Zoom Settings\")\n@export var min_distance: float = 2.0\n@export var max_distance: float = 15.0\n@export var zoom_speed: float = 0.5\n@export var zoom_smoothing: float = 10.0\n\n@export_group(\"FOV Settings\")\n@export var base_fov: float = 75.0\n@export var speed_fov_multiplier: float = 1.2\n@export var max_speed_fov: float = 90.0\n\n@export_group(\"Camera Shake\")\n@export var shake_intensity: float = 1.0\n@export var shake_decay: float = 5.0\n\n@export_group(\"Look Ahead\")\n@export var look_ahead_distance: float = 3.0\n@export var look_ahead_speed: float = 2.0\n\n# State\nvar current_mode: CameraMode = CameraMode.FOLLOW\nvar target_distance: float = 6.0\nvar yaw: float = 0.0\nvar pitch: float = 0.0\nvar min_pitch: float = -60.0\nvar max_pitch: float = 60.0\n\n# Dynamic camera\nvar current_fov: float = 75.0\nvar shake_offset: Vector3 = Vector3.ZERO\nvar shake_timer: float = 0.0\nvar look_ahead_offset: Vector3 = Vector3.ZERO\n\n# References\n@onready var camera: Camera3D = $Camera3D\nvar target: Node3D = null\nvar player_controller: PlayerController = null\n\n# Replay system\nvar replay_positions: Array = []\nvar replay_index: int = 0\nvar max_replay_frames: int = 300  # 5 seconds at 60fps\n\nsignal mode_changed(new_mode: CameraMode)\n\nfunc _ready():\n\tInput.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)\n\ttarget_distance = distance\n\tcurrent_fov = base_fov\n\tcamera.fov = current_fov\n\nfunc _input(event):\n\t# Camera mode switching\n\tif Input.is_action_just_pressed(\"camera_mode\"):\n\t\t_cycle_camera_mode()\n\n\t# Mouse look (only in follow mode)\n\tif current_mode == CameraMode.FOLLOW and event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:\n\t\tyaw -= event.relative.x * mouse_sensitivity\n\t\tpitch += event.relative.y * mouse_sensitivity\n\t\tpitch = clamp(pitch, deg_to_rad(min_pitch), deg_to_rad(max_pitch))\n\n\t# Zoom\n\tif event is InputEventMouseButton and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:\n\t\tif event.button_index == MOUSE_BUTTON_WHEEL_UP:\n\t\t\ttarget_distance -= zoom_speed\n\t\t\ttarget_distance = clamp(target_distance, min_distance, max_distance)\n\t\telif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:\n\t\t\ttarget_distance += zoom_speed\n\t\t\ttarget_distance = clamp(target_distance, min_distance, max_distance)\n\n\t# Toggle mouse capture\n\tif Input.is_action_just_pressed(\"ui_cancel\"):\n\t\tif Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:\n\t\t\tInput.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)\n\t\telse:\n\t\t\tInput.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)\n\nfunc _process(delta):\n\tif not target:\n\t\treturn\n\n\t# Update camera based on mode\n\tmatch current_mode:\n\t\tCameraMode.FOLLOW:\n\t\t\t_update_follow_camera(delta)\n\t\tCameraMode.CINEMATIC:\n\t\t\t_update_cinematic_camera(delta)\n\t\tCameraMode.REPLAY:\n\t\t\t_update_replay_camera(delta)\n\t\tCameraMode.FIRST_PERSON:\n\t\t\t_update_first_person_camera(delta)\n\n\t# Update dynamic FOV\n\t_update_dynamic_fov(delta)\n\n\t# Update camera shake\n\t_update_camera_shake(delta)\n\n\t# Record position for replay\n\t_record_replay_frame()\n\nfunc _update_follow_camera(delta):\n\t# Smooth zoom\n\tdistance = lerp(distance, target_distance, zoom_smoothing * delta)\n\n\t# Calculate look-ahead based on velocity\n\tif player_controller:\n\t\tvar velocity = Vector3.ZERO\n\t\tif player_controller.current_state == PlayerController.PlayerState.SKATEBOARDING and player_controller.skateboard_instance:\n\t\t\tvelocity = player_controller.skateboard_instance.linear_velocity\n\t\telif player_controller.current_state == PlayerController.PlayerState.WALKING:\n\t\t\tvelocity = player_controller.velocity\n\n\t\t# Calculate look-ahead offset\n\t\tvar target_look_ahead = velocity.normalized() * look_ahead_distance * min(velocity.length() / 10.0, 1.0)\n\t\tlook_ahead_offset = look_ahead_offset.lerp(target_look_ahead, look_ahead_speed * delta)\n\n\t# Calculate camera position\n\tvar target_pos = target.global_position + look_ahead_offset\n\n\t# Calculate offset based on yaw and pitch\n\tvar offset = Vector3()\n\toffset.x = cos(pitch) * sin(yaw) * distance\n\toffset.y = sin(pitch) * distance + height\n\toffset.z = cos(pitch) * cos(yaw) * distance\n\n\t# Apply shake\n\toffset += shake_offset\n\n\t# Set camera position\n\tvar desired_pos = target_pos + offset\n\tglobal_position = global_position.lerp(desired_pos, follow_speed * delta)\n\n\t# Look at target\n\tvar look_target = target_pos + Vector3(0, height, 0)\n\tlook_at(look_target, Vector3.UP)\n\nfunc _update_cinematic_camera(delta):\n\t# Cinematic camera follows at a fixed angle and distance\n\tvar target_pos = target.global_position\n\n\t# Fixed cinematic angle\n\tvar cinematic_offset = Vector3(5, 3, 5)\n\tglobal_position = global_position.lerp(target_pos + cinematic_offset, 2.0 * delta)\n\n\t# Always look at target\n\tlook_at(target_pos + Vector3(0, 1, 0), Vector3.UP)\n\nfunc _update_replay_camera(delta):\n\t# Play back recorded positions\n\tif replay_positions.size() > 0:\n\t\treplay_index = (replay_index + 1) % replay_positions.size()\n\t\tvar replay_data = replay_positions[replay_index]\n\t\tglobal_position = replay_data.position\n\t\tglobal_rotation = replay_data.rotation\n\nfunc _update_first_person_camera(delta):\n\t# First person view from player's perspective\n\tif player_controller:\n\t\tglobal_position = player_controller.global_position + Vector3(0, 1.7, 0)\n\n\t\t# Use player's rotation plus mouse look\n\t\tvar player_rotation = player_controller.global_rotation.y\n\t\trotation.y = player_rotation + yaw\n\t\trotation.x = pitch\n\nfunc _update_dynamic_fov(delta):\n\tvar target_fov = base_fov\n\n\t# Increase FOV based on speed\n\tif player_controller and player_controller.skateboard_instance:\n\t\tvar speed = player_controller.skateboard_instance.get_speed()\n\t\tvar speed_factor = min(speed / 15.0, 1.0)  # Max effect at 15 units/sec\n\t\ttarget_fov = base_fov + (max_speed_fov - base_fov) * speed_factor * speed_fov_multiplier\n\n\tcurrent_fov = lerp(current_fov, target_fov, 3.0 * delta)\n\tcamera.fov = current_fov\n\nfunc _update_camera_shake(delta):\n\tif shake_timer > 0:\n\t\tshake_timer -= delta\n\n\t\t# Generate random shake offset\n\t\tvar intensity = shake_timer * shake_intensity\n\t\tshake_offset = Vector3(\n\t\t\trandf_range(-intensity, intensity),\n\t\t\trandf_range(-intensity, intensity),\n\t\t\trandf_range(-intensity, intensity)\n\t\t)\n\n\t\t# Decay shake\n\t\tshake_timer = max(0, shake_timer - shake_decay * delta)\n\telse:\n\t\tshake_offset = Vector3.ZERO\n\nfunc _record_replay_frame():\n\tvar frame_data = {\n\t\t\"position\": global_position,\n\t\t\"rotation\": global_rotation\n\t}\n\n\treplay_positions.append(frame_data)\n\n\t# Keep only recent frames\n\tif replay_positions.size() > max_replay_frames:\n\t\treplay_positions.pop_front()\n\nfunc _cycle_camera_mode():\n\tmatch current_mode:\n\t\tCameraMode.FOLLOW:\n\t\t\tcurrent_mode = CameraMode.CINEMATIC\n\t\tCameraMode.CINEMATIC:\n\t\t\tcurrent_mode = CameraMode.REPLAY\n\t\tCameraMode.REPLAY:\n\t\t\tcurrent_mode = CameraMode.FIRST_PERSON\n\t\tCameraMode.FIRST_PERSON:\n\t\t\tcurrent_mode = CameraMode.FOLLOW\n\n\tmode_changed.emit(current_mode)\n\n# Public interface\nfunc set_target(new_target: Node3D):\n\ttarget = new_target\n\nfunc set_player_controller(controller: PlayerController):\n\tplayer_controller = controller\n\nfunc add_shake(intensity: float, duration: float):\n\tshake_timer = max(shake_timer, duration)\n\tshake_intensity = intensity\n\nfunc set_mode(mode: CameraMode):\n\tcurrent_mode = mode\n\tmode_changed.emit(current_mode)\n\n\n", "exports": ["@export_group(\"Basic Settings\")", "@export var mouse_sensitivity: float = 0.002", "@export var distance: float = 6.0", "@export var height: float = 2.0", "@export var follow_speed: float = 8.0", "@export_group(\"Zoom Settings\")", "@export var min_distance: float = 2.0", "@export var max_distance: float = 15.0", "@export var zoom_speed: float = 0.5", "@export var zoom_smoothing: float = 10.0", "@export_group(\"FOV Settings\")", "@export var base_fov: float = 75.0", "@export var speed_fov_multiplier: float = 1.2", "@export var max_speed_fov: float = 90.0", "@export_group(\"Camera Shake\")", "@export var shake_intensity: float = 1.0", "@export var shake_decay: float = 5.0", "@export_group(\"Look Ahead\")", "@export var look_ahead_distance: float = 3.0", "@export var look_ahead_speed: float = 2.0"], "functions": ["func _ready():", "func _input(event):", "func _process(delta):", "func _update_follow_camera(delta):", "func _update_cinematic_camera(delta):", "func _update_replay_camera(delta):", "func _update_first_person_camera(delta):", "func _update_dynamic_fov(delta):", "func _update_camera_shake(delta):", "func _record_replay_frame():", "func _cycle_camera_mode():", "func set_target(new_target: Node3D):", "func set_player_controller(controller: PlayerController):", "func add_shake(intensity: float, duration: float):", "func set_mode(mode: CameraMode):"], "language": "gd", "path": "res:///scripts/camera_controller.gd", "signals": ["signal mode_changed(new_mode: CameraMode)"]}, {"content": "extends Node3D\nclass_name LevelEnvironment\n\n# Level properties\n@export var ground_size: Vector2 = Vector2(100, 100)\n@export var ground_height: float = 0.1\n\n# References\n@onready var ground_mesh: MeshInstance3D = $Ground/MeshInstance3D\n@onready var ground_collision: StaticBody3D = $Ground\n@onready var ground_shape: CollisionShape3D = $Ground/CollisionShape3D\n\nfunc _ready():\n\t# Environment elements will be set up in the scene editor\n\t# Add to ground group for skateboard detection\n\tif ground_collision:\n\t\tground_collision.add_to_group(\"ground\")\n\n# Environment objects will be set up in the scene editor\n\nfunc get_spawn_position() -> Vector3:\n\treturn Vector3(0, 1, 0)\n\nfunc is_within_bounds(pos: Vector3) -> bool:\n\treturn abs(pos.x) < ground_size.x/2 and abs(pos.z) < ground_size.y/2\n", "exports": ["@export var ground_size: Vector2 = Vector2(100, 100)", "@export var ground_height: float = 0.1"], "functions": ["func _ready():", "func get_spawn_position() -> Vector3:", "func is_within_bounds(pos: Vector3) -> bool:"], "language": "gd", "path": "res:///scripts/level_environment.gd", "signals": []}, {"content": "extends Node3D\n\n# References to main components\n@onready var player = $Player\n@onready var camera_controller = $CameraController\n@onready var environment = $Environment\n\n# Skateboard scene reference\n@export var skateboard_scene: PackedScene\n\nfunc _ready():\n\t# Set up the skateboard scene reference for the player\n\tif skateboard_scene:\n\t\tplayer.skateboard_scene = skateboard_scene\n\telse:\n\t\t# Load the skateboard scene\n\t\tskateboard_scene = load(\"res://scenes/skateboard.tscn\")\n\t\tplayer.skateboard_scene = skateboard_scene\n\t\n\t# Set up camera to follow player\n\tcamera_controller.set_target(player)\n\tcamera_controller.set_player_controller(player)\n\n\t# Give player reference to camera for relative movement\n\tplayer.camera_controller = camera_controller\n\t\n\t# Position player at spawn point\n\tplayer.global_position = environment.get_spawn_position()\n\t\n\t# Set up lighting\n\t_setup_lighting()\n\nfunc _setup_lighting():\n\tvar light = $DirectionalLight3D\n\tlight.position = Vector3(0, 10, 5)\n\tlight.rotation_degrees = Vector3(-45, -30, 0)\n\tlight.light_energy = 1.0\n\tlight.shadow_enabled = true\n\nfunc _input(_event):\n\t# Handle any global input here if needed\n\tpass\n", "exports": ["@export var skateboard_scene: PackedScene"], "functions": ["func _ready():", "func _setup_lighting():", "func _input(_event):"], "language": "gd", "path": "res:///scripts/main.gd", "signals": []}, {"content": "extends CharacterBody3D\nclass_name PlayerController\n\n# Player states\nenum PlayerState {\n\tWALKING,\n\tSKATEBOARDING,\n\tBAILING  # New state for falls\n}\n\n# Movement properties\n@export_group(\"On Foot Movement\")\n@export var walk_speed: float = 5.0\n@export var run_speed: float = 8.0\n@export var jump_velocity: float = 4.5\n@export var acceleration: float = 10.0\n@export var friction: float = 10.0\n@export var air_control: float = 0.3\n\n@export_group(\"Skateboard Settings\")\n@export var skateboard_scene: PackedScene\n@export var mount_animation_time: float = 0.3\n@export var bail_threshold_speed: float = 18.0\n@export var bail_threshold_angle: float = 45.0\n\n# State\nvar current_state: PlayerState = PlayerState.WALKING\nvar skateboard_instance: RigidBody3D = null\nvar nearby_skateboard: RigidBody3D = null\n\n# Physics\nvar gravity: float = ProjectSettings.get_setting(\"physics/3d/default_gravity\")\nvar is_pushing: bool = false\nvar push_timer: float = 0.0\nvar push_cooldown: float = 0.3\n\n# Mounting/Dismounting\nvar is_mounting: bool = false\nvar mount_timer: float = 0.0\nvar mount_start_pos: Vector3\nvar mount_target_pos: Vector3\n\n# Bailing\nvar bail_velocity: Vector3 = Vector3.ZERO\nvar bail_timer: float = 0.0\n\n# Animation States\nvar current_trick: String = \"\"\nvar stance: String = \"regular\"  # or \"goofy\"\n\n# References\n@onready var mesh_instance: MeshInstance3D = $MeshInstance3D\n@onready var collision_shape: CollisionShape3D = $CollisionShape3D\n@onready var skateboard_pickup_area: Area3D = $SkateboardPickupArea\n@onready var deck_detection_area: Area3D = $DeckDetectionArea\nvar camera_controller\n\n# Input buffer for trick detection\nvar input_buffer: Array = []\nvar buffer_time: float = 0.3\nvar last_input_time: float = 0.0\n\n# Collision settings storage for skateboard mode\nvar original_collision_layer: int\nvar original_collision_mask: int\n\n# Deck surface detection\nvar is_on_deck_surface: bool = false\n\nsignal state_changed(new_state: PlayerState)\nsignal trick_performed(trick_name: String)\nsignal bailed()\n\nfunc _ready():\n\t# Store original collision settings\n\toriginal_collision_layer = collision_layer\n\toriginal_collision_mask = collision_mask\n\n\t# Connect area signals\n\tif skateboard_pickup_area:\n\t\tskateboard_pickup_area.body_entered.connect(_on_skateboard_area_entered)\n\t\tskateboard_pickup_area.body_exited.connect(_on_skateboard_area_exited)\n\n\tif deck_detection_area:\n\t\tdeck_detection_area.area_entered.connect(_on_deck_area_entered)\n\t\tdeck_detection_area.area_exited.connect(_on_deck_area_exited)\n\nfunc _physics_process(delta):\n\t# Update timers\n\tif push_timer > 0:\n\t\tpush_timer -= delta\n\n\t# State machine\n\tmatch current_state:\n\t\tPlayerState.WALKING:\n\t\t\t_handle_walking_physics(delta)\n\t\tPlayerState.SKATEBOARDING:\n\t\t\t_handle_skateboard_physics(delta)\n\t\tPlayerState.BAILING:\n\t\t\t_handle_bail_physics(delta)\n\n\t# Handle mounting animation\n\tif is_mounting:\n\t\t_update_mount_animation(delta)\n\nfunc _handle_walking_physics(delta):\n\t# Gravity\n\tif not is_on_floor():\n\t\tvelocity.y -= gravity * delta\n\n\t# Jump\n\tif Input.is_action_just_pressed(\"jump\") and is_on_floor():\n\t\tvelocity.y = jump_velocity\n\n\t# Get input\n\tvar input_dir = _get_movement_input()\n\n\t# Camera-relative movement\n\tvar direction = _get_camera_relative_direction(input_dir)\n\n\t# Apply movement\n\tif direction != Vector3.ZERO:\n\t\tvar target_speed = run_speed if Input.is_action_pressed(\"run\") else walk_speed\n\t\tvelocity.x = move_toward(velocity.x, direction.x * target_speed, acceleration * delta)\n\t\tvelocity.z = move_toward(velocity.z, direction.z * target_speed, acceleration * delta)\n\n\t\t# Rotate to face direction\n\t\tvar target_rotation = atan2(direction.x, direction.z)\n\t\trotation.y = lerp_angle(rotation.y, target_rotation, 10.0 * delta)\n\telse:\n\t\tvelocity.x = move_toward(velocity.x, 0, friction * delta)\n\t\tvelocity.z = move_toward(velocity.z, 0, friction * delta)\n\n\tmove_and_slide()\n\nfunc _handle_skateboard_physics(delta):\n\tif not skateboard_instance:\n\t\treturn\n\n\t# Get input\n\tvar input_dir = _get_movement_input()\n\tvar camera_dir = _get_camera_relative_direction(input_dir)\n\n\t# Calculate skateboard input\n\tvar forward_input = 0.0\n\tvar turn_input = 0.0\n\tvar lean_input = 0.0\n\n\t# Forward/backward from input\n\tif camera_dir != Vector3.ZERO:\n\t\t# Project camera direction onto skateboard's forward/right axes\n\t\tvar board_forward = -skateboard_instance.transform.basis.z\n\t\tvar board_right = skateboard_instance.transform.basis.x\n\n\t\tforward_input = camera_dir.dot(board_forward)\n\t\tturn_input = camera_dir.dot(board_right)\n\n\t\t# Normalize turn input\n\t\tturn_input = clamp(turn_input, -1.0, 1.0)\n\n\t# Pushing mechanic (space or push button)\n\tif Input.is_action_just_pressed(\"push\") and push_timer <= 0:\n\t\tforward_input = 1.0\n\t\tpush_timer = push_cooldown\n\t\tis_pushing = true\n\t\ttrick_performed.emit(\"push\")\n\n\t# Braking\n\tvar is_braking = Input.is_action_pressed(\"brake\")\n\n\t# Leaning for carves (shift keys or triggers)\n\tif Input.is_action_pressed(\"lean_left\"):\n\t\tlean_input = -1.0\n\telif Input.is_action_pressed(\"lean_right\"):\n\t\tlean_input = 1.0\n\n\t# Send input to skateboard\n\tif skateboard_instance.has_method(\"apply_input\"):\n\t\tskateboard_instance.apply_input(forward_input, turn_input, lean_input, is_braking)\n\n\t# Trick detection\n\t_handle_trick_input(delta)\n\n\t# Position player on skateboard\n\t_update_player_on_skateboard(delta)\n\n\t# Check for bail conditions\n\t_check_bail_conditions()\n\nfunc _handle_bail_physics(delta):\n\t# Ragdoll-like physics when bailing\n\tvelocity += Vector3(0, -gravity, 0) * delta\n\tvelocity *= 0.98  # Friction\n\n\t# Add some tumbling\n\trotation.x += bail_velocity.x * delta\n\trotation.z += bail_velocity.z * delta\n\n\tmove_and_slide()\n\n\t# Recover from bail\n\tbail_timer -= delta\n\tif bail_timer <= 0 and is_on_floor():\n\t\t_recover_from_bail()\n\nfunc _update_player_on_skateboard(delta):\n\tif not skateboard_instance:\n\t\treturn\n\n\t# Calculate target position on skateboard\n\tvar board_pos = skateboard_instance.global_position\n\tvar board_rot = skateboard_instance.global_rotation\n\n\t# Height offset based on skateboard state\n\tvar height_offset = 0.9\n\tif skateboard_instance.has_method(\"get_state\"):\n\t\tmatch skateboard_instance.get_state():\n\t\t\tskateboard_instance.BoardState.MANUAL:\n\t\t\t\theight_offset += 0.1\n\t\t\tskateboard_instance.BoardState.GRIND:\n\t\t\t\theight_offset += 0.05\n\n\t# Target position\n\tvar target_pos = board_pos + Vector3(0, height_offset, 0)\n\n\t# Smooth position\n\tglobal_position = global_position.lerp(target_pos, 20.0 * delta)\n\n\t# Match rotation with style offset for stance\n\tvar style_rotation = board_rot.y\n\tif stance == \"goofy\":\n\t\tstyle_rotation += PI\n\n\trotation.y = lerp_angle(rotation.y, style_rotation, 10.0 * delta)\n\nfunc _handle_trick_input(delta):\n\t# Detect trick combinations\n\t_update_input_buffer()\n\n\t# Ollie (Jump button)\n\tif Input.is_action_just_pressed(\"jump\"):\n\t\tif skateboard_instance and skateboard_instance.has_method(\"ollie\"):\n\t\t\tskateboard_instance.ollie()\n\t\t\ttrick_performed.emit(\"ollie\")\n\t\t\t_add_to_input_buffer(\"jump\")\n\n\t# Kickflip (Jump + Kick)\n\tif Input.is_action_just_pressed(\"kick\") and _is_in_air():\n\t\tif skateboard_instance and skateboard_instance.has_method(\"kickflip\"):\n\t\t\tskateboard_instance.kickflip()\n\t\t\ttrick_performed.emit(\"kickflip\")\n\n\t# Manual (Up/Down balance)\n\tif Input.is_action_just_pressed(\"manual\") and _is_on_ground():\n\t\tif skateboard_instance and skateboard_instance.has_method(\"manual\"):\n\t\t\tskateboard_instance.manual()\n\t\t\ttrick_performed.emit(\"manual\")\n\n\t# Grabs (in air only)\n\tif _is_in_air():\n\t\tif Input.is_action_pressed(\"grab_indy\"):\n\t\t\tcurrent_trick = \"indy\"\n\t\t\ttrick_performed.emit(\"indy_grab\")\n\t\telif Input.is_action_pressed(\"grab_melon\"):\n\t\t\tcurrent_trick = \"melon\"\n\t\t\ttrick_performed.emit(\"melon_grab\")\n\nfunc _check_bail_conditions():\n\tif not skateboard_instance:\n\t\treturn\n\n\t# Check speed threshold\n\tif skateboard_instance.get_speed() > bail_threshold_speed:\n\t\t# Random chance to bail at high speed\n\t\tif randf() < 0.001:\n\t\t\t_initiate_bail(\"speed_wobble\")\n\t\t\treturn\n\n\t# Check landing angle\n\tif skateboard_instance.has_method(\"get_landing_angle\"):\n\t\tvar landing_angle = skateboard_instance.get_landing_angle()\n\t\tif landing_angle > bail_threshold_angle:\n\t\t\t_initiate_bail(\"bad_landing\")\n\nfunc _initiate_bail(reason: String):\n\tif current_state == PlayerState.BAILING:\n\t\treturn\n\n\tcurrent_state = PlayerState.BAILING\n\tbail_timer = 2.0\n\n\t# Calculate bail velocity based on skateboard velocity\n\tif skateboard_instance:\n\t\tbail_velocity = skateboard_instance.linear_velocity * 0.5\n\t\tbail_velocity.y = abs(bail_velocity.y) + 2.0  # Add upward component\n\n\t\t# Disconnect from skateboard\n\t\t_get_off_skateboard()\n\n\t# Apply bail velocity\n\tvelocity = bail_velocity\n\n\t# Emit signal\n\tbailed.emit()\n\tstate_changed.emit(current_state)\n\nfunc _recover_from_bail():\n\tcurrent_state = PlayerState.WALKING\n\trotation.x = 0\n\trotation.z = 0\n\tstate_changed.emit(current_state)\n\nfunc _update_mount_animation(delta):\n\tmount_timer += delta\n\tvar t = mount_timer / mount_animation_time\n\n\tif t >= 1.0:\n\t\t# Animation complete\n\t\tis_mounting = false\n\t\tglobal_position = mount_target_pos\n\telse:\n\t\t# Smooth interpolation\n\t\tvar eased_t = ease(t, -1.5)  # Ease out\n\t\tglobal_position = mount_start_pos.lerp(mount_target_pos, eased_t)\n\nfunc _get_movement_input() -> Vector2:\n\tvar input = Vector2.ZERO\n\n\tif Input.is_action_pressed(\"move_forward\"):\n\t\tinput.y += 1\n\tif Input.is_action_pressed(\"move_backward\"):\n\t\tinput.y -= 1\n\tif Input.is_action_pressed(\"move_left\"):\n\t\tinput.x -= 1\n\tif Input.is_action_pressed(\"move_right\"):\n\t\tinput.x += 1\n\n\treturn input.normalized()\n\nfunc _get_camera_relative_direction(input: Vector2) -> Vector3:\n\tif not camera_controller:\n\t\treturn Vector3(input.x, 0, -input.y)\n\n\tvar cam_transform = camera_controller.global_transform\n\tvar forward = -cam_transform.basis.z\n\tforward.y = 0\n\tforward = forward.normalized()\n\n\tvar right = cam_transform.basis.x\n\tright.y = 0\n\tright = right.normalized()\n\n\treturn forward * input.y + right * input.x\n\nfunc _is_on_ground() -> bool:\n\tif skateboard_instance and skateboard_instance.has_method(\"is_on_ground\"):\n\t\treturn skateboard_instance.is_on_ground()\n\treturn is_on_floor()\n\nfunc _is_in_air() -> bool:\n\treturn not _is_on_ground()\n\nfunc _add_to_input_buffer(action: String):\n\tvar current_time = Time.get_ticks_msec() / 1000.0\n\tinput_buffer.append({\n\t\t\"action\": action,\n\t\t\"time\": current_time\n\t})\n\tlast_input_time = current_time\n\nfunc _update_input_buffer():\n\tvar current_time = Time.get_ticks_msec() / 1000.0\n\tinput_buffer = input_buffer.filter(func(entry):\n\t\treturn current_time - entry.time < buffer_time\n\t)\n\n# Public interface\nfunc _input(event):\n\tif event.is_action_pressed(\"interact_skateboard\"):\n\t\t_toggle_skateboard()\n\n\tif event.is_action_pressed(\"change_stance\"):\n\t\tstance = \"goofy\" if stance == \"regular\" else \"regular\"\n\nfunc _toggle_skateboard():\n\tif current_state == PlayerState.WALKING:\n\t\tif nearby_skateboard:\n\t\t\t_get_on_skateboard(nearby_skateboard)\n\t\telse:\n\t\t\t_spawn_skateboard()\n\telse:\n\t\t_get_off_skateboard()\n\nfunc _spawn_skateboard():\n\tif skateboard_scene:\n\t\tskateboard_instance = skateboard_scene.instantiate()\n\t\tget_parent().add_child(skateboard_instance)\n\n\t\t# Position skateboard on the ground under player's feet\n\t\t# Place skateboard at ground level (y = 0) and let physics handle it\n\t\tskateboard_instance.global_position = Vector3(global_position.x, 0.05, global_position.z)\n\n\t\t# Match skateboard rotation to player's current facing direction\n\t\t# Add PI/2 + PI to use opposite end as front\n\t\tskateboard_instance.rotation.y = rotation.y + PI/2 + PI\n\n\t\t_get_on_skateboard(skateboard_instance)\n\telse:\n\t\tpush_error(\"No skateboard scene assigned!\")\n\nfunc _get_on_skateboard(skateboard: RigidBody3D):\n\tskateboard_instance = skateboard\n\tcurrent_state = PlayerState.SKATEBOARDING\n\n\t# Start mounting animation\n\tis_mounting = true\n\tmount_timer = 0.0\n\tmount_start_pos = global_position\n\tmount_target_pos = skateboard.global_position + Vector3(0, 0.9, 0)\n\n\t# Connect to skateboard\n\tif skateboard_instance.has_method(\"set_player\"):\n\t\tskateboard_instance.set_player(self)\n\n\tstate_changed.emit(current_state)\n\nfunc _get_off_skateboard():\n\tif skateboard_instance:\n\t\t# Disconnect from skateboard\n\t\tif skateboard_instance.has_method(\"set_player\"):\n\t\t\tskateboard_instance.set_player(null)\n\n\t\t# Position player next to skateboard\n\t\tvar dismount_pos = skateboard_instance.global_position + Vector3(1.0, 0, 0)\n\t\tglobal_position = dismount_pos\n\n\t\t# Keep skateboard in world (don't despawn)\n\t\tskateboard_instance = null\n\n\tcurrent_state = PlayerState.WALKING\n\tis_mounting = false\n\n\tstate_changed.emit(current_state)\n\n# Helper function to determine if player should be considered \"on floor\"\n# When on skateboard, we check if standing on deck surface instead of ground\nfunc _is_player_on_floor() -> bool:\n\tif current_state == PlayerState.SKATEBOARDING:\n\t\treturn is_on_deck_surface  # When on skateboard, check deck surface\n\telse:\n\t\treturn is_on_floor()  # Normal floor detection when walking\n\nfunc _on_skateboard_area_entered(body):\n\tif body.is_in_group(\"skateboard\") and current_state == PlayerState.WALKING:\n\t\tnearby_skateboard = body\n\nfunc _on_skateboard_area_exited(body):\n\tif body == nearby_skateboard:\n\t\tnearby_skateboard = null\n\nfunc _on_deck_area_entered(area):\n\tif area.get_parent().is_in_group(\"skateboard\"):\n\t\tis_on_deck_surface = true\n\nfunc _on_deck_area_exited(area):\n\tif area.get_parent().is_in_group(\"skateboard\"):\n\t\tis_on_deck_surface = false\n\nfunc get_current_state() -> PlayerState:\n\treturn current_state\n\nfunc is_skateboarding() -> bool:\n\treturn current_state == PlayerState.SKATEBOARDING\n", "exports": ["@export_group(\"On Foot Movement\")", "@export var walk_speed: float = 5.0", "@export var run_speed: float = 8.0", "@export var jump_velocity: float = 4.5", "@export var acceleration: float = 10.0", "@export var friction: float = 10.0", "@export var air_control: float = 0.3", "@export_group(\"Skateboard Settings\")", "@export var skateboard_scene: PackedScene", "@export var mount_animation_time: float = 0.3", "@export var bail_threshold_speed: float = 18.0", "@export var bail_threshold_angle: float = 45.0"], "functions": ["func _ready():", "func _physics_process(delta):", "func _handle_walking_physics(delta):", "func _handle_skateboard_physics(delta):", "func _handle_bail_physics(delta):", "func _update_player_on_skateboard(delta):", "func _handle_trick_input(delta):", "func _check_bail_conditions():", "func _initiate_bail(reason: String):", "func _recover_from_bail():", "func _update_mount_animation(delta):", "func _get_movement_input() -> Vector2:", "func _get_camera_relative_direction(input: Vector2) -> Vector3:", "func _is_on_ground() -> bool:", "func _is_in_air() -> bool:", "func _add_to_input_buffer(action: String):", "func _update_input_buffer():", "func _input(event):", "func _toggle_skateboard():", "func _spawn_skateboard():", "func _get_on_skateboard(skateboard: RigidBody3D):", "func _get_off_skateboard():", "func _is_player_on_floor() -> bool:", "func _on_skateboard_area_entered(body):", "func _on_skateboard_area_exited(body):", "func _on_deck_area_entered(area):", "func _on_deck_area_exited(area):", "func get_current_state() -> PlayerState:", "func is_skateboarding() -> bool:"], "language": "gd", "path": "res:///scripts/player_controller.gd", "signals": ["signal state_changed(new_state: PlayerState)", "signal trick_performed(trick_name: String)", "signal bailed()"]}, {"content": "extends RigidBody3D\nclass_name Skateboard\n\n# Core Physics Properties\n@export_group(\"Movement\")\n@export var acceleration_force: float = 25.0\n@export var max_speed: float = 20.0\n@export var turn_rate: float = 2.5\n@export var carve_multiplier: float = 1.5\n@export var speed_wobble_threshold: float = 15.0\n@export var speed_wobble_intensity: float = 0.1\n\n@export_group(\"Momentum & Friction\")\n@export var rolling_resistance: float = 0.02\n@export var bearing_friction: float = 0.98\n@export var surface_friction_multiplier: float = 1.0\n@export var momentum_preservation: float = 0.97\n@export var brake_force: float = 15.0\n@export var powerslide_friction: float = 0.7\n\n@export_group(\"Board Feel\")\n@export var weight_distribution: float = 0.6  # 0.5 = centered, 1.0 = all front\n@export var lean_angle_max: float = 35.0  # degrees\n@export var lean_speed: float = 3.0\n@export var board_flex: float = 0.15\n@export var landing_compression: float = 0.3\n\n@export_group(\"Tricks\")\n@export var ollie_force: float = 8.0\n@export var ollie_torque: float = 5.0\n@export var flip_speed: float = 720.0  # degrees per second\n@export var grind_balance_speed: float = 2.0\n@export var manual_balance_difficulty: float = 1.5\n\n@export_group(\"Physics Response\")\n@export var suspension_stiffness: float = 150.0\n@export var suspension_damping: float = 15.0\n@export var wheel_radius: float = 0.03\n@export var truck_looseness: float = 0.8  # 0-1, affects turn responsiveness\n\n# State Management\nenum BoardState {\n\tGROUND,\n\tAIR,\n\tGRIND,\n\tMANUAL,\n\tPOWERSLIDE\n}\n\nvar current_state: BoardState = BoardState.GROUND\nvar previous_state: BoardState = BoardState.GROUND\n\n# Physics State\nvar board_velocity: Vector3 = Vector3.ZERO\nvar angular_momentum: Vector3 = Vector3.ZERO\nvar lean_angle: float = 0.0\nvar current_lean: float = 0.0\nvar compression: float = 0.0\nvar is_grounded: bool = false\nvar ground_normal: Vector3 = Vector3.UP\nvar surface_angle: float = 0.0\n\n# Trick State\nvar trick_rotation: Vector3 = Vector3.ZERO\nvar is_flipping: bool = false\nvar flip_axis: Vector3 = Vector3.ZERO\nvar grind_balance: float = 0.0\nvar manual_balance: float = 0.0\n\n# Input State\nvar input_forward: float = 0.0\nvar input_turn: float = 0.0\nvar input_lean: float = 0.0\nvar is_braking: bool = false\nvar is_powersliding: bool = false\n\n# References\n@onready var front_trucks: Node3D = $FrontWheels\n@onready var back_trucks: Node3D = $BackWheels\n@onready var ground_rays: Array[RayCast3D] = []\n@onready var mesh_instance: MeshInstance3D = $MeshInstance3D\n\n# Ground Detection\nvar ground_contacts: Array = []\nvar average_ground_height: float = 0.0\n\n# Player Reference\nvar player = null\n\n# Physics Materials\nvar current_surface_type: String = \"concrete\"\nvar surface_properties = {\n\t\"concrete\": {\"friction\": 1.0, \"bounce\": 0.1},\n\t\"wood\": {\"friction\": 0.9, \"bounce\": 0.15},\n\t\"metal\": {\"friction\": 0.7, \"bounce\": 0.05},\n\t\"grass\": {\"friction\": 1.3, \"bounce\": 0.3}\n}\n\nsignal state_changed(new_state: BoardState)\nsignal trick_landed(trick_name: String, score: int)\nsignal grind_started(rail_type: String)\nsignal grind_ended(duration: float)\nsignal player_mounted(player_ref)\nsignal player_dismounted()\n\nfunc _ready():\n\tadd_to_group(\"skateboard\")\n\n\t# Physics setup\n\tmass = 3.0\n\tgravity_scale = 1.2\n\tcontinuous_cd = true\n\tcontact_monitor = true\n\tmax_contacts_reported = 10\n\n\t# Improved physics material\n\tphysics_material_override = PhysicsMaterial.new()\n\tphysics_material_override.friction = 0.8\n\tphysics_material_override.bounce = 0.1\n\n\t# Set up damping\n\tlinear_damp = 0.1\n\tangular_damp = 1.0\n\n\t# Create ground detection rays\n\t_setup_ground_detection()\n\n\t# Connect collision detection\n\tbody_entered.connect(_on_body_entered)\n\tbody_exited.connect(_on_body_exited)\n\nfunc _setup_ground_detection():\n\t# Create 4 rays at each wheel position for accurate ground detection\n\tvar wheel_positions = [\n\t\tVector3(-0.15, 0, 0.3),   # Front left\n\t\tVector3(0.15, 0, 0.3),    # Front right\n\t\tVector3(-0.15, 0, -0.3),  # Back left\n\t\tVector3(0.15, 0, -0.3)    # Back right\n\t]\n\n\tfor pos in wheel_positions:\n\t\tvar ray = RayCast3D.new()\n\t\tadd_child(ray)\n\t\tray.position = pos\n\t\tray.target_position = Vector3(0, -wheel_radius * 2, 0)\n\t\tray.enabled = true\n\t\tground_rays.append(ray)\n\nfunc _physics_process(delta):\n\t# Update ground detection\n\t_update_ground_detection()\n\n\t# State machine\n\tmatch current_state:\n\t\tBoardState.GROUND:\n\t\t\t_physics_ground(delta)\n\t\tBoardState.AIR:\n\t\t\t_physics_air(delta)\n\t\tBoardState.GRIND:\n\t\t\t_physics_grind(delta)\n\t\tBoardState.MANUAL:\n\t\t\t_physics_manual(delta)\n\t\tBoardState.POWERSLIDE:\n\t\t\t_physics_powerslide(delta)\n\n\t# Apply board flex and compression\n\t_apply_board_dynamics(delta)\n\n\t# Update visual representation\n\t_update_visuals(delta)\n\nfunc _update_ground_detection():\n\tground_contacts.clear()\n\tvar total_height = 0.0\n\tvar contact_count = 0\n\n\tfor ray in ground_rays:\n\t\tif ray.is_colliding():\n\t\t\tvar contact_point = ray.get_collision_point()\n\t\t\tvar contact_normal = ray.get_collision_normal()\n\t\t\tground_contacts.append({\n\t\t\t\t\"point\": contact_point,\n\t\t\t\t\"normal\": contact_normal,\n\t\t\t\t\"distance\": ray.position.distance_to(contact_point)\n\t\t\t})\n\t\t\ttotal_height += contact_point.y\n\t\t\tcontact_count += 1\n\n\t# Update grounded state\n\tvar was_grounded = is_grounded\n\tis_grounded = contact_count >= 2  # At least 2 wheels touching\n\n\tif is_grounded:\n\t\taverage_ground_height = total_height / contact_count\n\n\t\t# Calculate average ground normal\n\t\tground_normal = Vector3.ZERO\n\t\tfor contact in ground_contacts:\n\t\t\tground_normal += contact.normal\n\t\tground_normal = ground_normal.normalized()\n\n\t\t# Calculate surface angle\n\t\tsurface_angle = rad_to_deg(acos(ground_normal.dot(Vector3.UP)))\n\n\t\t# State transitions\n\t\tif was_grounded == false:\n\t\t\t_on_landing()\n\telse:\n\t\tif was_grounded == true:\n\t\t\t_on_takeoff()\n\nfunc _physics_ground(delta):\n\t# Get current velocity\n\tvar current_velocity = linear_velocity\n\tvar speed = current_velocity.length()\n\n\t# Apply input acceleration\n\tif input_forward != 0:\n\t\tvar push_direction = -transform.basis.z\n\t\tvar push_force = acceleration_force * input_forward\n\n\t\t# Reduce effectiveness at high speeds\n\t\tvar speed_factor = clamp(1.0 - (speed / max_speed), 0.0, 1.0)\n\t\tpush_force *= speed_factor\n\n\t\tapply_central_force(push_direction * push_force)\n\n\t# Advanced turning physics\n\tif input_turn != 0 and speed > 0.1:\n\t\t# Calculate turn force based on speed (more speed = wider turns)\n\t\tvar speed_factor = clamp(speed / 10.0, 0.1, 1.0)\n\t\tvar turn_force = turn_rate * input_turn * speed_factor\n\n\t\t# Apply carving physics\n\t\tif abs(input_lean) > 0.1:\n\t\t\tturn_force *= carve_multiplier\n\n\t\t\t# Add lateral force for realistic carving\n\t\t\tvar carve_direction = transform.basis.x * sign(input_turn)\n\t\t\tapply_central_force(carve_direction * abs(input_lean) * speed * 0.5)\n\n\t\t# Apply turning torque\n\t\tapply_torque(Vector3.UP * turn_force)\n\n\t\t# Speed wobbles at high speed\n\t\tif speed > speed_wobble_threshold:\n\t\t\tvar wobble = sin(Time.get_ticks_msec() * 0.01) * speed_wobble_intensity\n\t\t\tapply_torque(Vector3.UP * wobble)\n\n\t# Rolling resistance and friction\n\tvar friction_force = -current_velocity.normalized() * rolling_resistance * speed\n\tfriction_force *= surface_properties[current_surface_type].friction\n\tapply_central_force(friction_force)\n\n\t# Bearing friction (speed-dependent)\n\tlinear_velocity *= bearing_friction\n\n\t# Brake physics\n\tif is_braking:\n\t\tvar brake_direction = -current_velocity.normalized()\n\t\tapply_central_force(brake_direction * brake_force)\n\n\t\t# Check for powerslide initiation\n\t\tif abs(input_turn) > 0.7 and speed > 5.0:\n\t\t\t_enter_powerslide()\n\n\t# Weight distribution affects steering\n\tvar weight_offset = (weight_distribution - 0.5) * 0.2\n\tcenter_of_mass = Vector3(0, 0, weight_offset)\n\nfunc _physics_air(delta):\n\t# Air control (limited)\n\tif input_turn != 0:\n\t\tvar air_turn_rate = turn_rate * 0.3\n\t\tapply_torque(Vector3.UP * input_turn * air_turn_rate)\n\n\t# Maintain angular momentum\n\tangular_velocity *= 0.98\n\n\t# Air resistance\n\tvar air_drag = -linear_velocity * 0.01\n\tapply_central_force(air_drag)\n\n\t# Trick rotations\n\tif is_flipping:\n\t\ttrick_rotation += flip_axis * flip_speed * delta\n\t\trotation = trick_rotation\n\nfunc _physics_grind(delta):\n\t# Grind physics would go here\n\t# Lock to rail, apply grinding friction, handle balance\n\tpass\n\nfunc _physics_manual(delta):\n\t# Manual balance physics\n\tmanual_balance += input_forward * manual_balance_difficulty * delta\n\tmanual_balance = clamp(manual_balance, -1.0, 1.0)\n\n\t# Apply rotation based on balance\n\tvar manual_angle = manual_balance * 30.0\n\trotation.x = deg_to_rad(manual_angle)\n\n\t# Fall out of manual if balance is lost\n\tif abs(manual_balance) > 0.9:\n\t\t_exit_manual()\n\nfunc _physics_powerslide(delta):\n\t# Reduced grip during powerslide\n\tvar slide_velocity = linear_velocity\n\tvar forward_component = slide_velocity.dot(-transform.basis.z)\n\tvar lateral_component = slide_velocity.dot(transform.basis.x)\n\n\t# Reduce lateral grip\n\tlateral_component *= powerslide_friction\n\n\t# Reconstruct velocity\n\tlinear_velocity = -transform.basis.z * forward_component + transform.basis.x * lateral_component\n\n\t# Exit powerslide conditions\n\tif !is_braking or linear_velocity.length() < 2.0:\n\t\t_exit_powerslide()\n\nfunc _apply_board_dynamics(delta):\n\t# Board flex simulation\n\tif is_grounded:\n\t\tvar target_compression = 0.0\n\n\t\t# Compression from landing or pumping\n\t\tif ground_contacts.size() > 0:\n\t\t\tvar average_force = linear_velocity.y * -0.1\n\t\t\ttarget_compression = clamp(average_force, 0.0, landing_compression)\n\n\t\tcompression = lerp(compression, target_compression, 10.0 * delta)\n\telse:\n\t\tcompression = lerp(compression, 0.0, 5.0 * delta)\n\n\t# Lean simulation\n\tcurrent_lean = lerp(current_lean, input_lean * lean_angle_max, lean_speed * delta)\n\nfunc _update_visuals(delta):\n\tif !mesh_instance:\n\t\treturn\n\n\t# Apply compression to mesh\n\tvar compression_scale = 1.0 - compression * 0.1\n\tmesh_instance.scale.y = compression_scale\n\n\t# Apply lean rotation\n\tmesh_instance.rotation.z = deg_to_rad(current_lean)\n\n\t# Truck rotation based on turning\n\tif front_trucks and back_trucks:\n\t\tvar truck_angle = input_turn * 15.0 * truck_looseness\n\t\tfront_trucks.rotation.y = deg_to_rad(truck_angle)\n\t\tback_trucks.rotation.y = deg_to_rad(-truck_angle * 0.5)\n\nfunc _on_landing():\n\tcompression = landing_compression\n\tcurrent_state = BoardState.GROUND\n\tstate_changed.emit(current_state)\n\n\t# Calculate landing quality\n\tvar landing_angle = rad_to_deg(ground_normal.angle_to(Vector3.UP))\n\tif landing_angle < 15.0:\n\t\t# Good landing\n\t\tpass\n\telse:\n\t\t# Sketchy landing - add instability\n\t\tapply_torque_impulse(Vector3(randf() - 0.5, 0, randf() - 0.5) * 2.0)\n\nfunc _on_takeoff():\n\tcurrent_state = BoardState.AIR\n\tstate_changed.emit(current_state)\n\nfunc _enter_powerslide():\n\tcurrent_state = BoardState.POWERSLIDE\n\tstate_changed.emit(current_state)\n\nfunc _exit_powerslide():\n\tcurrent_state = BoardState.GROUND\n\tstate_changed.emit(current_state)\n\nfunc _exit_manual():\n\tcurrent_state = BoardState.GROUND\n\tstate_changed.emit(current_state)\n\nfunc _on_body_entered(body):\n\t# Detect rails, ramps, etc.\n\tif body.is_in_group(\"rail\"):\n\t\t_start_grind(body)\n\nfunc _on_body_exited(body):\n\tif body.is_in_group(\"rail\") and current_state == BoardState.GRIND:\n\t\t_end_grind()\n\nfunc _start_grind(rail):\n\tcurrent_state = BoardState.GRIND\n\tgrind_started.emit(\"rail\")\n\t# Snap to rail logic would go here\n\nfunc _end_grind():\n\tvar grind_duration = 0.0  # Calculate actual duration\n\tgrind_ended.emit(grind_duration)\n\tcurrent_state = BoardState.AIR\n\n# Public interface for player controller\nfunc apply_input(forward: float, turn: float, lean: float, brake: bool):\n\tinput_forward = forward\n\tinput_turn = turn\n\tinput_lean = lean\n\tis_braking = brake\n\nfunc ollie():\n\tif current_state == BoardState.GROUND:\n\t\tapply_central_impulse(Vector3.UP * ollie_force)\n\t\tapply_torque_impulse(Vector3.RIGHT * ollie_torque)\n\t\tcurrent_state = BoardState.AIR\n\nfunc kickflip():\n\tif current_state == BoardState.AIR and !is_flipping:\n\t\tis_flipping = true\n\t\tflip_axis = Vector3.RIGHT\n\t\ttrick_rotation = rotation\n\nfunc manual():\n\tif current_state == BoardState.GROUND and linear_velocity.length() > 2.0:\n\t\tcurrent_state = BoardState.MANUAL\n\t\tmanual_balance = 0.0\n\nfunc set_player(p):\n\tplayer = p\n\nfunc get_speed() -> float:\n\treturn linear_velocity.length()\n\nfunc get_state() -> BoardState:\n\treturn current_state\n\nfunc is_on_ground() -> bool:\n\treturn is_grounded\n", "exports": ["@export_group(\"Movement\")", "@export var acceleration_force: float = 25.0", "@export var max_speed: float = 20.0", "@export var turn_rate: float = 2.5", "@export var carve_multiplier: float = 1.5", "@export var speed_wobble_threshold: float = 15.0", "@export var speed_wobble_intensity: float = 0.1", "@export_group(\"Momentum & Friction\")", "@export var rolling_resistance: float = 0.02", "@export var bearing_friction: float = 0.98", "@export var surface_friction_multiplier: float = 1.0", "@export var momentum_preservation: float = 0.97", "@export var brake_force: float = 15.0", "@export var powerslide_friction: float = 0.7", "@export_group(\"Board Feel\")", "@export var weight_distribution: float = 0.6  # 0.5 = centered, 1.0 = all front", "@export var lean_angle_max: float = 35.0  # degrees", "@export var lean_speed: float = 3.0", "@export var board_flex: float = 0.15", "@export var landing_compression: float = 0.3", "@export_group(\"Tricks\")", "@export var ollie_force: float = 8.0", "@export var ollie_torque: float = 5.0", "@export var flip_speed: float = 720.0  # degrees per second", "@export var grind_balance_speed: float = 2.0", "@export var manual_balance_difficulty: float = 1.5", "@export_group(\"Physics Response\")", "@export var suspension_stiffness: float = 150.0", "@export var suspension_damping: float = 15.0", "@export var wheel_radius: float = 0.03", "@export var truck_looseness: float = 0.8  # 0-1, affects turn responsiveness"], "functions": ["func _ready():", "func _setup_ground_detection():", "func _physics_process(delta):", "func _update_ground_detection():", "func _physics_ground(delta):", "func _physics_air(delta):", "func _physics_grind(delta):", "func _physics_manual(delta):", "func _physics_powerslide(delta):", "func _apply_board_dynamics(delta):", "func _update_visuals(delta):", "func _on_landing():", "func _on_takeoff():", "func _enter_powerslide():", "func _exit_powerslide():", "func _exit_manual():", "func _on_body_entered(body):", "func _on_body_exited(body):", "func _start_grind(rail):", "func _end_grind():", "func apply_input(forward: float, turn: float, lean: float, brake: bool):", "func ollie():", "func kickflip():", "func manual():", "func set_player(p):", "func get_speed() -> float:", "func get_state() -> BoardState:", "func is_on_ground() -> bool:"], "language": "gd", "path": "res:///scripts/skateboard.gd", "signals": ["signal state_changed(new_state: BoardState)", "signal trick_landed(trick_name: String, score: int)", "signal grind_started(rail_type: String)", "signal grind_ended(duration: float)", "signal player_mounted(player_ref)", "signal player_dismounted()"]}]}