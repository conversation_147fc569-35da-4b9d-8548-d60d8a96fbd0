{"autoloads": {}, "generated_timestamp": "2025-07-28T17:19:32", "project_name": "skate", "project_settings": {"application": {"description": "", "main_scene": "res://scenes/main.tscn", "name": "skate"}, "display": {"height": 648, "width": 1152}, "input": {"map": {"spatial_editor/freelook_backwards": ["InputEventKey: keycode=83 (S), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_down": ["InputEventKey: keycode=81 (Q), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_forward": ["InputEventKey: keycode=87 (W), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_left": ["InputEventKey: keycode=65 (A), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_right": ["InputEventKey: keycode=68 (D), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_slow_modifier": ["InputEventKey: keycode=4194328 (Alt), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_speed_modifier": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/freelook_up": ["InputEventKey: keycode=69 (E), mods=none, physical=true, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_orbit_modifier_1": [], "spatial_editor/viewport_orbit_modifier_2": [], "spatial_editor/viewport_pan_modifier_1": ["InputEventKey: keycode=4194325 (Shift), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_pan_modifier_2": [], "spatial_editor/viewport_zoom_modifier_1": ["InputEventKey: keycode=4194326 (Ctrl), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "spatial_editor/viewport_zoom_modifier_2": [], "ui_accept": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_accessibility_drag_and_drop": [], "ui_cancel": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_colorpicker_delete_preset": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_copy": ["InputEventKey: keycode=67 (C), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_cut": ["InputEventKey: keycode=88 (X), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194312 (Delete), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_refresh": ["InputEventKey: keycode=4194336 (F5), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_show_hidden": ["InputEventKey: keycode=72 (H), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_filedialog_up_one_level": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_mode": ["InputEventKey: keycode=77 (M), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_next": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_focus_prev": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_duplicate": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_follow_left": ["InputEventKey: keycode=4194319 (Left), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_graph_follow_right": ["InputEventKey: keycode=4194321 (Right), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_home": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_menu": ["InputEventKey: keycode=4194370 (Menu), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_paste": ["InputEventKey: keycode=86 (V), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194311 (Insert), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_redo": ["InputEventKey: keycode=90 (Z), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=89 (Y), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_select": ["InputEventKey: keycode=32 (Space), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_swap_input_direction": ["InputEventKey: keycode=96 (QuoteLeft), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_add_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace": ["InputEventKey: keycode=4194308 (Backspace), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194308 (Backspace), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_backspace_all_to_left": [], "ui_text_backspace_word": ["InputEventKey: keycode=4194308 (Backspace), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_above": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_add_below": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_end": ["InputEventKey: keycode=4194318 (End), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_document_start": ["InputEventKey: keycode=4194317 (Home), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_down": ["InputEventKey: keycode=4194322 (Down), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_left": ["InputEventKey: keycode=4194319 (Left), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_end": ["InputEventKey: keycode=4194318 (End), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_line_start": ["InputEventKey: keycode=4194317 (Home), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_down": ["InputEventKey: keycode=4194324 (PageDown), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_page_up": ["InputEventKey: keycode=4194323 (PageUp), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_right": ["InputEventKey: keycode=4194321 (Right), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_left": ["InputEventKey: keycode=4194319 (Left), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_caret_word_right": ["InputEventKey: keycode=4194321 (Right), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_clear_carets_and_selection": ["InputEventKey: keycode=4194305 (Escape), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_accept": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_query": ["InputEventKey: keycode=32 (Space), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_completion_replace": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_dedent": ["InputEventKey: keycode=4194306 (Tab), mods=Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete": ["InputEventKey: keycode=4194312 (Delete), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_delete_all_to_right": [], "ui_text_delete_word": ["InputEventKey: keycode=4194312 (Delete), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_indent": ["InputEventKey: keycode=4194306 (Tab), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_above": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_newline_blank": ["InputEventKey: keycode=4194309 (Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_down": ["InputEventKey: keycode=4194322 (Down), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_scroll_up": ["InputEventKey: keycode=4194320 (Up), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_all": ["InputEventKey: keycode=65 (A), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_select_word_under_caret": ["InputEventKey: keycode=71 (G), mods=Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_skip_selection_for_next_occurrence": ["InputEventKey: keycode=68 (D), mods=Ctrl+Alt, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_submit": ["InputEventKey: keycode=4194309 (Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false", "InputEventKey: keycode=4194310 (Kp Enter), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_text_toggle_insert_mode": ["InputEventKey: keycode=4194311 (Insert), mods=none, physical=false, location=unspecified, pressed=false, echo=false"], "ui_undo": ["InputEventKey: keycode=90 (Z), mods=Ctrl, physical=false, location=unspecified, pressed=false, echo=false"], "ui_unicode_start": ["InputEventKey: keycode=85 (U), mods=Ctrl+Shift, physical=false, location=unspecified, pressed=false, echo=false"], "ui_up": ["InputEventKey: keycode=4194320 (Up), mods=none, physical=false, location=unspecified, pressed=false, echo=false"]}}, "physics": {}, "rendering": {}}, "scenes": [{"path": "res:///scenes/main.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "output", "properties": {"mesh": "res://assets/models/skater/skater.glb::A<PERSON><PERSON><PERSON><PERSON>_arjlb", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "SkaterModel", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "MeshInstance3D", "properties": {}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, -0.100000023841858, 0.0], "shape": "res://scenes/main.tscn::CapsuleShape3D_1"}, "script": "", "type": "CollisionShape3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "SkateboardPickupArea", "properties": {}, "script": "", "type": "Area3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_feet"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "DeckDetectionArea", "properties": {"position": [0.0, -0.899999976158142, 0.0]}, "script": "", "type": "Area3D"}], "groups": [], "name": "Player", "properties": {"acceleration": 10.0, "friction": 10.0, "jump_velocity": 4.5, "position": [0.0, 1.0, 0.0], "rotation": [0.0, 3.14159250259399, 0.0], "run_speed": 8.0, "script": "res://scripts/player_controller.gd", "skateboard_scene": "res://scenes/skateboard.tscn", "walk_speed": 5.0}, "script": "res://scripts/player_controller.gd", "type": "CharacterBody3D"}, {"children": [{"children": [], "groups": [], "name": "Camera3D", "properties": {"rotation": [-0.523599028587341, 0.0, 0.0]}, "script": "", "type": "Camera3D"}, {"children": [], "groups": [], "name": "CameraPivot", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "CameraController", "properties": {"position": [0.0, 2.0, 5.0], "script": "res://scripts/camera_controller.gd"}, "script": "res://scripts/camera_controller.gd", "type": "Node3D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_1", "mesh": "res://scenes/main.tscn::BoxMesh_1", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_1"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "Ground", "properties": {}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_4", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "RedCube", "properties": {"position": [10.0, 0.5, 10.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_5", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "GreenCube", "properties": {"position": [-10.0, 0.5, 10.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_6", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "BlueCube", "properties": {"position": [10.0, 0.5, -10.0]}, "script": "", "type": "StaticBody3D"}, {"children": [{"children": [], "groups": [], "name": "MeshInstance3D", "properties": {"material_override": "res://scenes/main.tscn::StandardMaterial3D_7", "mesh": "res://scenes/main.tscn::BoxMesh_3", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"shape": "res://scenes/main.tscn::BoxShape3D_2"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "YellowCube", "properties": {"position": [-10.0, 0.5, -10.0]}, "script": "", "type": "StaticBody3D"}], "groups": [], "name": "ReferenceCubes", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Environment", "properties": {"ground_height": 0.1, "ground_size": [100.0, 100.0], "script": "res://scripts/level_environment.gd"}, "script": "res://scripts/level_environment.gd", "type": "Node3D"}, {"children": [], "groups": [], "name": "DirectionalLight3D", "properties": {"position": [0.0, 10.0, 5.0], "rotation": [-0.785398066043854, 0.785398185253143, 0.0], "shadow_enabled": true}, "script": "", "type": "DirectionalLight3D"}], "groups": [], "name": "Main", "properties": {"script": "res://scripts/main.gd"}, "script": "res://scripts/main.gd", "type": "Node3D"}}, {"path": "res:///scenes/skateboard.tscn", "root_node": {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [], "groups": [], "name": "Skateboard_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>y<PERSON><PERSON>_wrj8j", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard_1", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON><PERSON><PERSON><PERSON>_wyxst", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [{"children": [], "groups": [], "name": "Skateboard2_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::<PERSON><PERSON><PERSON><PERSON><PERSON>_pouom", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "Skateboard2", "properties": {}, "script": "", "type": "Node3D"}, {"children": [{"children": [], "groups": [], "name": "Skateboard1_0", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>y<PERSON><PERSON>_kefqw", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_02", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_ys1ok", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_1", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::ArrayMesh_o0tmw", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_2", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_b5vg4", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "Skateboard1_22", "properties": {"mesh": "res://assets/models/skateboard/skateboard.glb::A<PERSON>yMesh_rkbod", "skeleton": "", "surface_material_override/0": "<Object#null>"}, "script": "", "type": "MeshInstance3D"}], "groups": [], "name": "Skateboard1", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Skateboard", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Root", "properties": {}, "script": "", "type": "Node3D"}], "groups": [], "name": "Sketchfab_model", "properties": {"rotation": [-1.57079637050629, 0.0, 0.0]}, "script": "", "type": "Node3D"}], "groups": [], "name": "SkateboardModel", "properties": {"position": [0.0, 0.0377334654331207, 0.0], "scale": [0.174999997019768, 0.174999997019768, 0.174999997019768]}, "script": "", "type": "Node3D"}], "groups": [], "name": "MeshInstance3D", "properties": {}, "script": "", "type": "MeshInstance3D"}, {"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [0.0, 0.0257413238286972, 0.0], "shape": "res://scenes/skateboard.tscn::BoxShape3D_1"}, "script": "", "type": "CollisionShape3D"}, {"children": [], "groups": [], "name": "FrontWheels", "properties": {"position": [0.0, -0.0500000007450581, 0.400000005960464]}, "script": "", "type": "Node3D"}, {"children": [{"children": [], "groups": [], "name": "CollisionShape3D", "properties": {"position": [-0.00161132216453552, 0.145416378974915, 0.0132660418748856], "shape": "res://scenes/skateboard.tscn::BoxShape3D_deck"}, "script": "", "type": "CollisionShape3D"}], "groups": [], "name": "DeckSurface", "properties": {}, "script": "", "type": "Area3D"}, {"children": [], "groups": [], "name": "BackWheels", "properties": {"position": [0.0, -0.0500000007450581, -0.400000005960464]}, "script": "", "type": "Node3D"}], "groups": ["skateboard"], "name": "Skateboard", "properties": {"air_resistance": 0.02, "carving_factor": 0.7, "friction_coefficient": 0.98, "mass": 2.0, "max_speed": 20.0, "momentum_preservation": 0.95, "push_angle_tolerance": 45.0, "push_cooldown": 0.15, "push_force": 15.0, "script": "res://scripts/skateboard.gd", "turn_force": 3.0, "turn_speed_influence": 0.5, "wheel_friction": 0.8, "wheel_grip": 0.9}, "script": "res://scripts/skateboard.gd", "type": "RigidBody3D"}}], "scripts": [{"content": "@tool\nextends RefCounted\n\nfunc export_project_structure() -> bool:\n\tvar output = {\n\t\t\"project_name\": ProjectSettings.get_setting(\"application/config/name\", \"Unknown\"),\n\t\t\"generated_timestamp\": Time.get_datetime_string_from_system(),\n\t\t\"scenes\": [],\n\t\t\"scripts\": [],\n\t\t\"autoloads\": {},\n\t\t\"project_settings\": get_relevant_project_settings()\n\t}\n\t\n\t# Get all scene files\n\tvar scene_files = find_files_recursive(\"res://\", \"tscn\")\n\tvar total_files = scene_files.size()\n\t\n\tprint(\"AI Context Generator: Found %d scenes to process...\" % total_files)\n\t\n\t# Process each scene\n\tfor i in range(scene_files.size()):\n\t\tvar scene_path = scene_files[i]\n\t\t\n\t\tprint(\"AI Context Generator: Processing scene %d/%d: %s\" % [i+1, total_files, scene_path.get_file()])\n\t\t\n\t\tvar scene_data = parse_scene_structure(scene_path)\n\t\tif scene_data:\n\t\t\toutput.scenes.append(scene_data)\n\t\n\t# Get all script files\n\tvar script_files = find_files_recursive(\"res://\", \"gd\")\n\tscript_files.append_array(find_files_recursive(\"res://\", \"cs\"))\n\t\n\tprint(\"AI Context Generator: Processing %d scripts...\" % script_files.size())\n\t\n\t# Process each script\n\tfor i in range(script_files.size()):\n\t\tvar script_path = script_files[i]\n\t\t\n\t\tvar script_data = extract_script_info(script_path)\n\t\tif script_data:\n\t\t\toutput.scripts.append(script_data)\n\t\n\t# Get autoloads\n\toutput.autoloads = get_autoloads()\n\t\n\t# Save to file\n\tvar json_string = JSON.stringify(output, \"\\t\")\n\tvar file = FileAccess.open(\"res://ai_context_export.json\", FileAccess.WRITE)\n\t\n\tif file:\n\t\tfile.store_string(json_string)\n\t\tfile.close()\n\t\t\n\t\t# Copy to clipboard\n\t\tDisplayServer.clipboard_set(json_string)\n\t\t\n\t\tprint(\"AI Context Generator: Successfully generated ai_context_export.json\")\n\t\tprint(\"AI Context Generator: JSON data copied to clipboard!\")\n\t\treturn true\n\telse:\n\t\tprint(\"AI Context Generator: Failed to write export file\")\n\t\treturn false\n\nfunc find_files_recursive(path: String, extension: String) -> Array[String]:\n\tvar files: Array[String] = []\n\tvar dir = DirAccess.open(path)\n\t\n\tif dir:\n\t\tdir.list_dir_begin()\n\t\tvar file_name = dir.get_next()\n\t\t\n\t\twhile file_name != \"\":\n\t\t\tvar full_path = path + \"/\" + file_name\n\t\t\t\n\t\t\tif dir.current_is_dir() and not file_name.begins_with(\".\"):\n\t\t\t\tfiles.append_array(find_files_recursive(full_path, extension))\n\t\t\telif file_name.get_extension() == extension:\n\t\t\t\tfiles.append(full_path)\n\t\t\t\n\t\t\tfile_name = dir.get_next()\n\t\t\n\t\tdir.list_dir_end()\n\t\n\treturn files\n\nfunc parse_scene_structure(scene_path: String) -> Dictionary:\n\tvar scene = load(scene_path) as PackedScene\n\tif not scene:\n\t\treturn {}\n\t\n\tvar instance = scene.instantiate()\n\tif not instance:\n\t\treturn {}\n\t\n\tvar scene_data = {\n\t\t\"path\": scene_path,\n\t\t\"root_node\": parse_node_recursive(instance)\n\t}\n\t\n\tinstance.queue_free()\n\treturn scene_data\n\nfunc parse_node_recursive(node: Node) -> Dictionary:\n\tvar node_data = {\n\t\t\"name\": node.name,\n\t\t\"type\": node.get_class(),\n\t\t\"script\": \"\",\n\t\t\"properties\": {},\n\t\t\"groups\": [],\n\t\t\"children\": []\n\t}\n\t\n\t# Get script path if attached\n\tif node.get_script():\n\t\tnode_data.script = node.get_script().resource_path\n\t\n\t# Get non-default properties\n\tnode_data.properties = get_non_default_properties(node)\n\t\n\t# Get groups\n\tfor group in node.get_groups():\n\t\tnode_data.groups.append(group)\n\t\n\t# Process children\n\tfor child in node.get_children():\n\t\tnode_data.children.append(parse_node_recursive(child))\n\t\n\treturn node_data\n\nfunc get_non_default_properties(node: Node) -> Dictionary:\n\tvar properties = {}\n\t\n\t# Create a default instance of the same type for comparison\n\tvar default_node = null\n\tvar node_class = node.get_class()\n\t\n\t# Try to create default instance\n\tif ClassDB.can_instantiate(node_class):\n\t\tdefault_node = ClassDB.instantiate(node_class)\n\t\n\tif not default_node:\n\t\treturn properties\n\t\n\t# Get all properties\n\tvar property_list = node.get_property_list()\n\t\n\tfor prop in property_list:\n\t\t# Only include editor-visible properties that aren't built-in engine properties\n\t\tif (prop.usage & PROPERTY_USAGE_EDITOR) and not prop.name.begins_with(\"_\"):\n\t\t\tvar current_value = node.get(prop.name)\n\t\t\tvar default_value = default_node.get(prop.name)\n\t\t\t\n\t\t\t# Compare values (handle different types appropriately)\n\t\t\tif not values_equal(current_value, default_value):\n\t\t\t\tproperties[prop.name] = serialize_value(current_value)\n\t\n\tdefault_node.queue_free()\n\treturn properties\n\nfunc values_equal(a, b) -> bool:\n\t# Handle Vector2, Vector3, Color, etc.\n\tif typeof(a) != typeof(b):\n\t\treturn false\n\t\n\tif a is Vector2 or a is Vector3 or a is Vector4:\n\t\treturn a.is_equal_approx(b)\n\telif a is Color:\n\t\treturn a.is_equal_approx(b)\n\telse:\n\t\treturn a == b\n\nfunc serialize_value(value):\n\t# Convert complex types to serializable formats\n\tif value is Vector2:\n\t\treturn [value.x, value.y]\n\telif value is Vector3:\n\t\treturn [value.x, value.y, value.z]\n\telif value is Vector4:\n\t\treturn [value.x, value.y, value.z, value.w]\n\telif value is Color:\n\t\treturn [value.r, value.g, value.b, value.a]\n\telif value is Resource and value.resource_path != \"\":\n\t\treturn value.resource_path\n\telse:\n\t\treturn value\n\nfunc extract_script_info(script_path: String) -> Dictionary:\n\tvar file = FileAccess.open(script_path, FileAccess.READ)\n\tif not file:\n\t\treturn {}\n\t\n\tvar content = file.get_as_text()\n\tfile.close()\n\t\n\tvar script_data = {\n\t\t\"path\": script_path,\n\t\t\"language\": script_path.get_extension(),\n\t\t\"content\": content,\n\t\t\"exports\": extract_exported_variables(content),\n\t\t\"signals\": extract_signals(content),\n\t\t\"functions\": extract_functions(content)\n\t}\n\t\n\treturn script_data\n\nfunc extract_exported_variables(content: String) -> Array:\n\tvar exports = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"@export\"):\n\t\t\texports.append(line)\n\t\n\treturn exports\n\nfunc extract_signals(content: String) -> Array:\n\tvar signals = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"signal \"):\n\t\t\tsignals.append(line)\n\t\n\treturn signals\n\nfunc extract_functions(content: String) -> Array:\n\tvar functions = []\n\tvar lines = content.split(\"\\n\")\n\t\n\tfor line in lines:\n\t\tline = line.strip_edges()\n\t\tif line.begins_with(\"func \"):\n\t\t\tfunctions.append(line)\n\t\n\treturn functions\n\nfunc get_autoloads() -> Dictionary:\n\tvar autoloads = {}\n\t\n\t# Get autoload settings from project settings\n\tvar settings = ProjectSettings.get_property_list()\n\t\n\tfor setting in settings:\n\t\tif setting.name.begins_with(\"autoload/\"):\n\t\t\tvar autoload_name = setting.name.substr(9)  # Remove \"autoload/\" prefix\n\t\t\tvar autoload_path = ProjectSettings.get_setting(setting.name)\n\t\t\tautoloads[autoload_name] = autoload_path\n\t\n\treturn autoloads\n\nfunc get_relevant_project_settings() -> Dictionary:\n\tvar settings = {\n\t\t\"application\": {},\n\t\t\"display\": {},\n\t\t\"input\": {},\n\t\t\"physics\": {},\n\t\t\"rendering\": {}\n\t}\n\t\n\t# Application settings\n\tsettings.application[\"name\"] = ProjectSettings.get_setting(\"application/config/name\", \"\")\n\tsettings.application[\"description\"] = ProjectSettings.get_setting(\"application/config/description\", \"\")\n\tsettings.application[\"main_scene\"] = ProjectSettings.get_setting(\"application/run/main_scene\", \"\")\n\t\n\t# Display settings\n\tsettings.display[\"width\"] = ProjectSettings.get_setting(\"display/window/size/viewport_width\", 0)\n\tsettings.display[\"height\"] = ProjectSettings.get_setting(\"display/window/size/viewport_height\", 0)\n\t\n\t# Input map\n\tvar input_map = {}\n\tfor action in InputMap.get_actions():\n\t\tinput_map[action] = []\n\t\tfor event in InputMap.action_get_events(action):\n\t\t\tinput_map[action].append(str(event))\n\tsettings.input[\"map\"] = input_map\n\t\n\treturn settings\n", "exports": [], "functions": ["func export_project_structure() -> bool:", "func find_files_recursive(path: String, extension: String) -> Array[String]:", "func parse_scene_structure(scene_path: String) -> Dictionary:", "func parse_node_recursive(node: Node) -> Dictionary:", "func get_non_default_properties(node: Node) -> Dictionary:", "func values_equal(a, b) -> bool:", "func serialize_value(value):", "func extract_script_info(script_path: String) -> Dictionary:", "func extract_exported_variables(content: String) -> Array:", "func extract_signals(content: String) -> Array:", "func extract_functions(content: String) -> Array:", "func get_autoloads() -> Dictionary:", "func get_relevant_project_settings() -> Dictionary:"], "language": "gd", "path": "res:///addons/ai_context_generator/ai_context_generator.gd", "signals": []}, {"content": "@tool\nextends EditorPlugin\n\nconst AIContextGenerator = preload(\"res://addons/ai_context_generator/ai_context_generator.gd\")\n\nfunc _enter_tree():\n\t# Add a simple menu item to the Project menu\n\tadd_tool_menu_item(\"Generate AI Context\", _on_generate_context)\n\nfunc _exit_tree():\n\t# Clean up\n\tremove_tool_menu_item(\"Generate AI Context\")\n\nfunc _on_generate_context():\n\tvar generator = AIContextGenerator.new()\n\tgenerator.export_project_structure()\n", "exports": [], "functions": ["func _enter_tree():", "func _exit_tree():", "func _on_generate_context():"], "language": "gd", "path": "res:///addons/ai_context_generator/plugin.gd", "signals": []}, {"content": "extends Node3D\n\n# Simple third-person camera\nvar mouse_sensitivity: float = 0.002\nvar distance: float = 6.0\nvar height: float = 2.0\n\n# Camera rotation\nvar yaw: float = 0.0\nvar pitch: float = 0.0\nvar min_pitch: float = -60.0\nvar max_pitch: float = 60.0\n\n# References\n@onready var camera: Camera3D = $Camera3D\nvar target\n\nfunc _ready():\n\t# Capture mouse\n\tInput.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)\n\nfunc _input(event):\n\tif event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:\n\t\t# Rotate camera with mouse\n\t\tyaw -= event.relative.x * mouse_sensitivity\n\t\tpitch += event.relative.y * mouse_sensitivity  # Reversed for inverted controls\n\t\tpitch = clamp(pitch, deg_to_rad(min_pitch), deg_to_rad(max_pitch))\n\n\t# Toggle mouse capture\n\tif Input.is_action_just_pressed(\"ui_cancel\"):\n\t\tif Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:\n\t\t\tInput.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)\n\t\telse:\n\t\t\tInput.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)\n\nfunc _process(_delta):\n\tif target:\n\t\t# Calculate camera position around target\n\t\tvar target_pos = target.global_position\n\n\t\t# Calculate offset based on yaw and pitch\n\t\tvar offset = Vector3()\n\t\toffset.x = cos(pitch) * sin(yaw) * distance\n\t\toffset.y = sin(pitch) * distance + height\n\t\toffset.z = cos(pitch) * cos(yaw) * distance\n\n\t\t# Set camera position\n\t\tglobal_position = target_pos + offset\n\n\t\t# Look at target\n\t\tlook_at(target_pos + Vector3(0, height, 0), Vector3.UP)\n\nfunc set_target(new_target):\n\ttarget = new_target\n\n\n", "exports": [], "functions": ["func _ready():", "func _input(event):", "func _process(_delta):", "func set_target(new_target):"], "language": "gd", "path": "res:///scripts/camera_controller.gd", "signals": []}, {"content": "extends Node3D\nclass_name LevelEnvironment\n\n# Level properties\n@export var ground_size: Vector2 = Vector2(100, 100)\n@export var ground_height: float = 0.1\n\n# References\n@onready var ground_mesh: MeshInstance3D = $Ground/MeshInstance3D\n@onready var ground_collision: StaticBody3D = $Ground\n@onready var ground_shape: CollisionShape3D = $Ground/CollisionShape3D\n\nfunc _ready():\n\t# Environment elements will be set up in the scene editor\n\t# Add to ground group for skateboard detection\n\tif ground_collision:\n\t\tground_collision.add_to_group(\"ground\")\n\n# Environment objects will be set up in the scene editor\n\nfunc get_spawn_position() -> Vector3:\n\treturn Vector3(0, 1, 0)\n\nfunc is_within_bounds(pos: Vector3) -> bool:\n\treturn abs(pos.x) < ground_size.x/2 and abs(pos.z) < ground_size.y/2\n", "exports": ["@export var ground_size: Vector2 = Vector2(100, 100)", "@export var ground_height: float = 0.1"], "functions": ["func _ready():", "func get_spawn_position() -> Vector3:", "func is_within_bounds(pos: Vector3) -> bool:"], "language": "gd", "path": "res:///scripts/level_environment.gd", "signals": []}, {"content": "extends Node3D\n\n# References to main components\n@onready var player = $Player\n@onready var camera_controller = $CameraController\n@onready var environment = $Environment\n\n# Skateboard scene reference\n@export var skateboard_scene: PackedScene\n\nfunc _ready():\n\t# Set up the skateboard scene reference for the player\n\tif skateboard_scene:\n\t\tplayer.skateboard_scene = skateboard_scene\n\telse:\n\t\t# Load the skateboard scene\n\t\tskateboard_scene = load(\"res://scenes/skateboard.tscn\")\n\t\tplayer.skateboard_scene = skateboard_scene\n\t\n\t# Set up camera to follow player\n\tcamera_controller.set_target(player)\n\n\t# Give player reference to camera for relative movement\n\tplayer.camera_controller = camera_controller\n\t\n\t# Position player at spawn point\n\tplayer.global_position = environment.get_spawn_position()\n\t\n\t# Set up lighting\n\t_setup_lighting()\n\nfunc _setup_lighting():\n\tvar light = $DirectionalLight3D\n\tlight.position = Vector3(0, 10, 5)\n\tlight.rotation_degrees = Vector3(-45, -30, 0)\n\tlight.light_energy = 1.0\n\tlight.shadow_enabled = true\n\nfunc _input(_event):\n\t# Handle any global input here if needed\n\tpass\n", "exports": ["@export var skateboard_scene: PackedScene"], "functions": ["func _ready():", "func _setup_lighting():", "func _input(_event):"], "language": "gd", "path": "res:///scripts/main.gd", "signals": []}, {"content": "extends CharacterBody3D\nclass_name Player<PERSON>ontroller\n\n# Player states\nenum PlayerState {\n\tWALKING,\n\tSKATEBOARDING\n}\n\n# Movement properties\n@export var walk_speed: float = 5.0\n@export var run_speed: float = 8.0\n@export var jump_velocity: float = 4.5\n@export var acceleration: float = 10.0\n@export var friction: float = 10.0\n\n# Skateboard properties\n@export var skateboard_scene: PackedScene\nvar skateboard_instance: RigidBody3D\nvar is_on_skateboard: bool = false\nvar current_state: PlayerState = PlayerState.WALKING\n\n# Physics\nvar gravity: float = ProjectSettings.get_setting(\"physics/3d/default_gravity\")\n\n# References\n@onready var mesh_instance: MeshInstance3D = $MeshInstance3D\n@onready var collision_shape: CollisionShape3D = $CollisionShape3D\n@onready var skateboard_pickup_area: Area3D = $SkateboardPickupArea\nvar camera_controller\n\n# Skateboard interaction\nvar nearby_skateboard: RigidBody3D = null\n\n# Collision settings storage for skateboard mode\nvar original_collision_layer: int\nvar original_collision_mask: int\n\n# Deck surface detection\nvar is_on_deck_surface: bool = false\n\nsignal state_changed(new_state: PlayerState)\n\nfunc _ready():\n\t# Store original collision settings\n\toriginal_collision_layer = collision_layer\n\toriginal_collision_mask = collision_mask\n\n\t# Connect skateboard pickup area signals\n\tif skateboard_pickup_area:\n\t\tskateboard_pickup_area.body_entered.connect(_on_skateboard_area_entered)\n\t\tskateboard_pickup_area.body_exited.connect(_on_skateboard_area_exited)\n\n\t# Connect deck detection area\n\tvar deck_area = $DeckDetectionArea\n\tdeck_area.area_entered.connect(_on_deck_area_entered)\n\tdeck_area.area_exited.connect(_on_deck_area_exited)\n\n\t# Create player visual representation\n\t_create_player_mesh()\n\nfunc _create_player_mesh():\n\t# Player mesh and collision will be set up in the scene editor\n\t# This function can be removed or used for any runtime setup if needed\n\tpass\n\nfunc _physics_process(delta):\n\tmatch current_state:\n\t\tPlayerState.WALKING:\n\t\t\t_handle_walking_physics(delta)\n\t\tPlayerState.SKATEBOARDING:\n\t\t\t_handle_skateboard_physics(delta)\n\nfunc _handle_walking_physics(delta):\n\t# Ensure mesh is in normal orientation when walking\n\tmesh_instance.rotation.y = 0\n\n\t# Add gravity\n\tif not _is_player_on_floor():\n\t\tvelocity.y -= gravity * delta\n\n\t# Handle jump\n\tif Input.is_action_just_pressed(\"ui_accept\") and _is_player_on_floor():\n\t\tvelocity.y = jump_velocity\n\n\t# Get input direction\n\tvar input_dir = Vector2.ZERO\n\tif Input.is_action_pressed(\"move_forward\"):\n\t\tinput_dir.y += 1\n\tif Input.is_action_pressed(\"move_backward\"):\n\t\tinput_dir.y -= 1\n\tif Input.is_action_pressed(\"move_left\"):\n\t\tinput_dir.x -= 1\n\tif Input.is_action_pressed(\"move_right\"):\n\t\tinput_dir.x += 1\n\n\t# Normalize input to prevent faster diagonal movement\n\tinput_dir = input_dir.normalized()\n\n\t# Get camera direction for relative movement\n\tvar camera_forward = Vector3.ZERO\n\tvar camera_right = Vector3.ZERO\n\n\tif camera_controller:\n\t\t# Get camera's forward direction (projected onto horizontal plane)\n\t\tvar cam_transform = camera_controller.global_transform\n\t\tcamera_forward = -cam_transform.basis.z\n\t\tcamera_forward.y = 0  # Remove vertical component\n\t\tcamera_forward = camera_forward.normalized()\n\n\t\t# Get camera's right direction\n\t\tcamera_right = cam_transform.basis.x\n\t\tcamera_right.y = 0  # Remove vertical component\n\t\tcamera_right = camera_right.normalized()\n\telse:\n\t\t# Fallback to world directions if no camera\n\t\tcamera_forward = Vector3(0, 0, -1)\n\t\tcamera_right = Vector3(1, 0, 0)\n\n\t# Calculate movement direction relative to camera\n\tvar direction = camera_forward * input_dir.y + camera_right * input_dir.x\n\n\t# Determine speed based on run input\n\tvar target_speed = run_speed if Input.is_action_pressed(\"run\") else walk_speed\n\n\tif direction != Vector3.ZERO:\n\t\t# Move relative to camera direction\n\t\tvelocity.x = direction.x * target_speed\n\t\tvelocity.z = direction.z * target_speed\n\n\t\t# Rotate player to face movement direction\n\t\tvar target_rotation = atan2(direction.x, direction.z)\n\t\trotation.y = lerp_angle(rotation.y, target_rotation, 8.0 * delta)\n\telse:\n\t\t# Stop horizontal movement when no input\n\t\tvelocity.x = 0\n\t\tvelocity.z = 0\n\n\tmove_and_slide()\n\nfunc _handle_skateboard_physics(delta):\n\t# When on skateboard, handle simple movement\n\tif skateboard_instance:\n\t\t# Handle skateboard movement input\n\t\t_handle_skateboard_movement(delta)\n\n\t\t# Keep player positioned on top of skateboard (feet on deck surface)\n\t\t# Get the actual deck surface position from the skateboard\n\t\tvar deck_surface_area = skateboard_instance.get_node(\"DeckSurface\")\n\t\tvar deck_collision_shape = deck_surface_area.get_node(\"CollisionShape3D\")\n\t\tvar deck_surface_y = deck_collision_shape.global_position.y\n\t\t# Player feet should be just above the deck surface\n\t\t# Account for collision shape offset (-0.1) and half capsule height (0.9)\n\t\tvar player_y = deck_surface_y + 0.9 + 0.1  # Total offset: 1.0 units above deck surface\n\t\tglobal_position = Vector3(skateboard_instance.global_position.x, player_y, skateboard_instance.global_position.z)\n\n\t\t# Match skateboard rotation for turning\n\t\t# Rotate the visual model for goofy stance instead of the CharacterBody3D\n\t\trotation.y = skateboard_instance.rotation.y - PI/2  # Keep physics rotation normal\n\t\tmesh_instance.rotation.y = PI/2  # Rotate visual model 90 degrees for goofy stance\n\nfunc _handle_skateboard_movement(_delta):\n\t# Simple skateboard movement similar to walking\n\tif not skateboard_instance:\n\t\treturn\n\n\t# Get input direction\n\tvar input_dir = Vector2.ZERO\n\tif Input.is_action_pressed(\"move_forward\"):\n\t\tinput_dir.y += 1\n\tif Input.is_action_pressed(\"move_backward\"):\n\t\tinput_dir.y -= 1\n\tif Input.is_action_pressed(\"move_left\"):\n\t\tinput_dir.x -= 1\n\tif Input.is_action_pressed(\"move_right\"):\n\t\tinput_dir.x += 1\n\n\t# Normalize input to prevent faster diagonal movement\n\tinput_dir = input_dir.normalized()\n\n\t# Get camera direction for relative movement\n\tvar camera_forward = Vector3.ZERO\n\tvar camera_right = Vector3.ZERO\n\n\tif camera_controller:\n\t\t# Get camera's forward direction (projected onto horizontal plane)\n\t\tvar cam_transform = camera_controller.global_transform\n\t\tcamera_forward = -cam_transform.basis.z\n\t\tcamera_forward.y = 0  # Remove vertical component\n\t\tcamera_forward = camera_forward.normalized()\n\n\t\t# Get camera's right direction\n\t\tcamera_right = cam_transform.basis.x\n\t\tcamera_right.y = 0  # Remove vertical component\n\t\tcamera_right = camera_right.normalized()\n\telse:\n\t\t# Fallback to world directions if no camera\n\t\tcamera_forward = Vector3(0, 0, -1)\n\t\tcamera_right = Vector3(1, 0, 0)\n\n\t# Calculate movement direction relative to camera\n\tvar direction = camera_forward * input_dir.y + camera_right * input_dir.x\n\n\t# Apply movement to skateboard\n\tif direction != Vector3.ZERO:\n\t\tvar skateboard_speed = 8.0  # Similar to walking speed\n\t\tvar movement_force = direction * skateboard_speed\n\n\t\t# Apply the movement as velocity to the skateboard's RigidBody3D\n\t\t# Preserve Y velocity (gravity) while setting X and Z movement\n\t\tskateboard_instance.linear_velocity.x = movement_force.x\n\t\tskateboard_instance.linear_velocity.z = movement_force.z\n\n\t\t# Rotate skateboard to face movement direction\n\t\tvar target_rotation = atan2(direction.x, direction.z) + PI/2\n\t\tskateboard_instance.rotation.y = target_rotation\n\telse:\n\t\t# When not moving, stop horizontal movement but preserve gravity\n\t\tskateboard_instance.linear_velocity.x = 0\n\t\tskateboard_instance.linear_velocity.z = 0\n\n# Skateboard input handling removed - ready for new system\n\nfunc _input(event):\n\tif event.is_action_pressed(\"interact_skateboard\"):\n\t\t_toggle_skateboard()\n\nfunc _toggle_skateboard():\n\tif current_state == PlayerState.WALKING:\n\t\tif nearby_skateboard:\n\t\t\t_get_on_skateboard(nearby_skateboard)\n\t\telse:\n\t\t\t_spawn_skateboard()\n\telse:\n\t\t_get_off_skateboard()\n\nfunc _spawn_skateboard():\n\tif skateboard_scene:\n\t\tskateboard_instance = skateboard_scene.instantiate()\n\t\tget_parent().add_child(skateboard_instance)\n\n\t\t# Position skateboard on the ground under player's feet\n\t\t# Place skateboard at ground level (y = 0) and let physics handle it\n\t\tskateboard_instance.global_position = Vector3(global_position.x, 0.05, global_position.z)\n\n\t\t# Match skateboard rotation to player's current facing direction\n\t\t# Add 90 degrees (PI/2) so skateboard front faces forward\n\t\tskateboard_instance.rotation.y = rotation.y + PI/2\n\n\t\t_get_on_skateboard(skateboard_instance)\n\telse:\n\t\tpush_error(\"No skateboard scene assigned!\")\n\nfunc _get_on_skateboard(skateboard: RigidBody3D):\n\tskateboard_instance = skateboard\n\tis_on_skateboard = true\n\tcurrent_state = PlayerState.SKATEBOARDING\n\n\t# Store current player rotation to preserve facing direction\n\tvar player_rotation = rotation.y\n\n\t# Position player on top of skateboard (feet on deck surface)\n\t# Get the actual deck surface position from the skateboard\n\tvar deck_surface_area = skateboard_instance.get_node(\"DeckSurface\")\n\tvar deck_collision_shape = deck_surface_area.get_node(\"CollisionShape3D\")\n\tvar deck_surface_y = deck_collision_shape.global_position.y\n\n\t# Player feet should be just above the deck surface\n\t# Account for collision shape offset (-0.1) and half capsule height (0.9)\n\tvar player_y = deck_surface_y + 0.9 + 0.1  # Total offset: 1.0 units above deck surface\n\tglobal_position = Vector3(skateboard_instance.global_position.x, player_y, skateboard_instance.global_position.z)\n\n\t# Preserve player's facing direction (don't change rotation)\n\trotation.y = player_rotation\n\n\t# Properly disable collision detection when on skateboard\n\t# Instead of just disabling the CollisionShape3D, we need to change collision layers\n\t# Disable collision with ground (layer 1) by setting collision_mask to 0\n\t# This prevents CharacterBody3D from detecting floor collisions\n\tcollision_mask = 0\n\tcollision_layer = 0\n\n\t# Connect to skateboard\n\tif skateboard_instance.has_method(\"set_player\"):\n\t\tskateboard_instance.set_player(self)\n\n\tstate_changed.emit(current_state)\n\nfunc _get_off_skateboard():\n\tif skateboard_instance:\n\t\t# Keep player in exact same position - no movement when getting off\n\t\t# Player should just \"plop down\" where they are\n\n\t\t# Disconnect from skateboard\n\t\tif skateboard_instance.has_method(\"set_player\"):\n\t\t\tskateboard_instance.set_player(null)\n\n\t\t# Remove skateboard from scene (despawn it)\n\t\tskateboard_instance.queue_free()\n\t\tskateboard_instance = null\n\n\tis_on_skateboard = false\n\tcurrent_state = PlayerState.WALKING\n\n\t# Restore original collision settings\n\tcollision_layer = original_collision_layer\n\tcollision_mask = original_collision_mask\n\n\tstate_changed.emit(current_state)\n\n# Helper function to determine if player should be considered \"on floor\"\n# When on skateboard, we check if standing on deck surface instead of ground\nfunc _is_player_on_floor() -> bool:\n\tif current_state == PlayerState.SKATEBOARDING:\n\t\treturn is_on_deck_surface  # When on skateboard, check deck surface\n\telse:\n\t\treturn is_on_floor()  # Normal floor detection when walking\n\nfunc _on_skateboard_area_entered(body):\n\tif body.is_in_group(\"skateboard\") and not is_on_skateboard:\n\t\tnearby_skateboard = body\n\nfunc _on_skateboard_area_exited(body):\n\tif body == nearby_skateboard:\n\t\tnearby_skateboard = null\n\nfunc _on_deck_area_entered(area):\n\tif area.get_parent().is_in_group(\"skateboard\"):\n\t\tis_on_deck_surface = true\n\nfunc _on_deck_area_exited(area):\n\tif area.get_parent().is_in_group(\"skateboard\"):\n\t\tis_on_deck_surface = false\n\nfunc get_current_state() -> PlayerState:\n\treturn current_state\n\nfunc is_skateboarding() -> bool:\n\treturn current_state == PlayerState.SKATEBOARDING\n", "exports": ["@export var walk_speed: float = 5.0", "@export var run_speed: float = 8.0", "@export var jump_velocity: float = 4.5", "@export var acceleration: float = 10.0", "@export var friction: float = 10.0", "@export var skateboard_scene: PackedScene"], "functions": ["func _ready():", "func _create_player_mesh():", "func _physics_process(delta):", "func _handle_walking_physics(delta):", "func _handle_skateboard_physics(delta):", "func _handle_skateboard_movement(_delta):", "func _input(event):", "func _toggle_skateboard():", "func _spawn_skateboard():", "func _get_on_skateboard(skateboard: RigidBody3D):", "func _get_off_skateboard():", "func _is_player_on_floor() -> bool:", "func _on_skateboard_area_entered(body):", "func _on_skateboard_area_exited(body):", "func _on_deck_area_entered(area):", "func _on_deck_area_exited(area):", "func get_current_state() -> PlayerState:", "func is_skateboarding() -> bool:"], "language": "gd", "path": "res:///scripts/player_controller.gd", "signals": ["signal state_changed(new_state: PlayerState)"]}, {"content": "extends RigidBody3D\nclass_name Skateboard\n\n# Skateboard physics properties\n@export var push_force: float = 15.0\n@export var turn_force: float = 3.0\n@export var max_speed: float = 20.0\n@export var friction_coefficient: float = 0.98\n@export var air_resistance: float = 0.02\n@export var push_cooldown: float = 0.15\n\n# Wheel properties\n@export var wheel_friction: float = 0.8\n@export var wheel_grip: float = 0.9\n\n# EA Skate-like physics\n@export var carving_factor: float = 0.7\n@export var momentum_preservation: float = 0.95\n@export var turn_speed_influence: float = 0.5\n@export var push_angle_tolerance: float = 45.0\n\n# References\n@onready var mesh_instance: MeshInstance3D = $MeshInstance3D\n@onready var collision_shape: CollisionShape3D = $CollisionShape3D\n@onready var front_wheels: Node3D = $FrontWheels\n@onready var back_wheels: Node3D = $BackWheels\n\n# State\nvar player = null\nvar push_timer: float = 0.0\nvar is_grounded: bool = true\n\n# Input tracking (received from player controller)\nvar turn_input: float = 0.0\nvar push_input: bool = false\nvar brake_input: bool = false\n\nsignal player_mounted(player_ref)\nsignal player_dismounted()\n\nfunc _ready():\n\t# Add to skateboard group for identification\n\tadd_to_group(\"skateboard\")\n\t\n\t# Create skateboard visual\n\t_create_skateboard_mesh()\n\t\n\t# Set physics properties\n\tmass = 2.0\n\tgravity_scale = 1.0\n\t# Prevent the skateboard from tipping over easily\n\tlock_rotation = false\n\t# Add some angular damping to prevent excessive spinning\n\tangular_damp = 2.0\n\tlinear_damp = 0.1\n\nfunc _create_skateboard_mesh():\n\t# Skateboard mesh and collision will be set up in the scene editor\n\t# This function can be removed or used for any runtime setup if needed\n\tpass\n\n\n\nfunc _physics_process(_delta):\n\t# Skateboard movement system removed - will be rebuilt\n\tpass\n\n# New method to receive input from player controller\nfunc handle_player_input(_push: bool, _turn: float, _brake: bool, _delta: float):\n\t# Movement system removed - will be rebuilt\n\tpass\n\n\n\n# All skateboard physics functions removed\n\n# All movement functions removed - ready for new system\n\nfunc set_player(new_player):\n\tif player and new_player == null:\n\t\t# Player dismounting\n\t\tplayer = null\n\t\tplayer_dismounted.emit()\n\telif new_player and not player:\n\t\t# Player mounting\n\t\tplayer = new_player\n\t\tplayer_mounted.emit(player)\n\n# Ground detection will be handled differently\n# For now, assume always grounded for simplicity\n\nfunc get_speed() -> float:\n\treturn linear_velocity.length()\n\nfunc is_moving() -> bool:\n\treturn linear_velocity.length() > 0.1\n", "exports": ["@export var push_force: float = 15.0", "@export var turn_force: float = 3.0", "@export var max_speed: float = 20.0", "@export var friction_coefficient: float = 0.98", "@export var air_resistance: float = 0.02", "@export var push_cooldown: float = 0.15", "@export var wheel_friction: float = 0.8", "@export var wheel_grip: float = 0.9", "@export var carving_factor: float = 0.7", "@export var momentum_preservation: float = 0.95", "@export var turn_speed_influence: float = 0.5", "@export var push_angle_tolerance: float = 45.0"], "functions": ["func _ready():", "func _create_skateboard_mesh():", "func _physics_process(_delta):", "func handle_player_input(_push: bool, _turn: float, _brake: bool, _delta: float):", "func set_player(new_player):", "func get_speed() -> float:", "func is_moving() -> bool:"], "language": "gd", "path": "res:///scripts/skateboard.gd", "signals": ["signal player_mounted(player_ref)", "signal player_dismounted()"]}]}