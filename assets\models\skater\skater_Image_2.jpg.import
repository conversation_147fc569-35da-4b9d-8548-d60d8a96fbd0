[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://btoecon5kp11e"
path="res://.godot/imported/skater_Image_2.jpg-3d6dedc3fc0f227961db226fd998d21c.ctex"
metadata={
"vram_texture": false
}
generator_parameters={
"md5": "8103c7da855b71b26f9651ee1118fc13"
}

[deps]

source_file="res://assets/models/skater/skater_Image_2.jpg"
dest_files=["res://.godot/imported/skater_Image_2.jpg-3d6dedc3fc0f227961db226fd998d21c.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/uastc_level=0
compress/rdo_quality_loss=0.0
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://assets/models/skater/skater_Image_2.jpg"
process/channel_remap/red=0
process/channel_remap/green=1
process/channel_remap/blue=2
process/channel_remap/alpha=3
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
