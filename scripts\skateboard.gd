extends RigidBody3D
class_name Skateboard

# Core Physics Properties
@export_group("Movement")
@export var acceleration_force: float = 25.0
@export var max_speed: float = 20.0
@export var turn_rate: float = 2.5
@export var carve_multiplier: float = 1.5
@export var speed_wobble_threshold: float = 15.0
@export var speed_wobble_intensity: float = 0.1

@export_group("Momentum & Friction")
@export var rolling_resistance: float = 0.02
@export var bearing_friction: float = 0.98
@export var surface_friction_multiplier: float = 1.0
@export var momentum_preservation: float = 0.97
@export var brake_force: float = 15.0
@export var powerslide_friction: float = 0.7

@export_group("Board Feel")
@export var weight_distribution: float = 0.6  # 0.5 = centered, 1.0 = all front
@export var lean_angle_max: float = 35.0  # degrees
@export var lean_speed: float = 3.0
@export var board_flex: float = 0.15
@export var landing_compression: float = 0.3

@export_group("Tricks")
@export var ollie_force: float = 8.0
@export var ollie_torque: float = 5.0
@export var flip_speed: float = 720.0  # degrees per second
@export var grind_balance_speed: float = 2.0
@export var manual_balance_difficulty: float = 1.5

@export_group("Physics Response")
@export var suspension_stiffness: float = 150.0
@export var suspension_damping: float = 15.0
@export var wheel_radius: float = 0.03
@export var truck_looseness: float = 0.8  # 0-1, affects turn responsiveness

# State Management
enum BoardState {
	GROUND,
	AIR,
	GRIND,
	MANUAL,
	POWERSLIDE
}

var current_state: BoardState = BoardState.GROUND
var previous_state: BoardState = BoardState.GROUND

# Physics State
var board_velocity: Vector3 = Vector3.ZERO
var angular_momentum: Vector3 = Vector3.ZERO
var lean_angle: float = 0.0
var current_lean: float = 0.0
var compression: float = 0.0
var is_grounded: bool = false
var ground_normal: Vector3 = Vector3.UP
var surface_angle: float = 0.0

# Trick State
var trick_rotation: Vector3 = Vector3.ZERO
var is_flipping: bool = false
var flip_axis: Vector3 = Vector3.ZERO
var grind_balance: float = 0.0
var manual_balance: float = 0.0

# Input State
var input_forward: float = 0.0
var input_turn: float = 0.0
var input_lean: float = 0.0
var is_braking: bool = false
var is_powersliding: bool = false

# References
@onready var front_trucks: Node3D = $FrontWheels
@onready var back_trucks: Node3D = $BackWheels
@onready var ground_rays: Array[RayCast3D] = []
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D

# Ground Detection
var ground_contacts: Array = []
var average_ground_height: float = 0.0

# Player Reference
var player = null

# Physics Materials
var current_surface_type: String = "concrete"
var surface_properties = {
	"concrete": {"friction": 1.0, "bounce": 0.1},
	"wood": {"friction": 0.9, "bounce": 0.15},
	"metal": {"friction": 0.7, "bounce": 0.05},
	"grass": {"friction": 1.3, "bounce": 0.3}
}

signal state_changed(new_state: BoardState)
signal trick_landed(trick_name: String, score: int)
signal grind_started(rail_type: String)
signal grind_ended(duration: float)
signal player_mounted(player_ref)
signal player_dismounted()

func _ready():
	add_to_group("skateboard")

	# Physics setup
	mass = 3.0
	gravity_scale = 1.2
	continuous_cd = true
	contact_monitor = true
	max_contacts_reported = 10

	# Improved physics material
	physics_material_override = PhysicsMaterial.new()
	physics_material_override.friction = 0.8
	physics_material_override.bounce = 0.1

	# Set up damping
	linear_damp = 0.1
	angular_damp = 1.0

	# Create ground detection rays
	_setup_ground_detection()

	# Connect collision detection
	body_entered.connect(_on_body_entered)
	body_exited.connect(_on_body_exited)

func _setup_ground_detection():
	# Create 4 rays at each wheel position for accurate ground detection
	var wheel_positions = [
		Vector3(-0.15, 0, 0.3),   # Front left
		Vector3(0.15, 0, 0.3),    # Front right
		Vector3(-0.15, 0, -0.3),  # Back left
		Vector3(0.15, 0, -0.3)    # Back right
	]

	for pos in wheel_positions:
		var ray = RayCast3D.new()
		add_child(ray)
		ray.position = pos
		ray.target_position = Vector3(0, -wheel_radius * 2, 0)
		ray.enabled = true
		ground_rays.append(ray)

func _physics_process(delta):
	# Update ground detection
	_update_ground_detection()

	# State machine
	match current_state:
		BoardState.GROUND:
			_physics_ground(delta)
		BoardState.AIR:
			_physics_air(delta)
		BoardState.GRIND:
			_physics_grind(delta)
		BoardState.MANUAL:
			_physics_manual(delta)
		BoardState.POWERSLIDE:
			_physics_powerslide(delta)

	# Apply board flex and compression
	_apply_board_dynamics(delta)

	# Update visual representation
	_update_visuals(delta)

func _update_ground_detection():
	ground_contacts.clear()
	var total_height = 0.0
	var contact_count = 0

	for ray in ground_rays:
		if ray.is_colliding():
			var contact_point = ray.get_collision_point()
			var contact_normal = ray.get_collision_normal()
			ground_contacts.append({
				"point": contact_point,
				"normal": contact_normal,
				"distance": ray.position.distance_to(contact_point)
			})
			total_height += contact_point.y
			contact_count += 1

	# Update grounded state
	var was_grounded = is_grounded
	is_grounded = contact_count >= 2  # At least 2 wheels touching

	if is_grounded:
		average_ground_height = total_height / contact_count

		# Calculate average ground normal
		ground_normal = Vector3.ZERO
		for contact in ground_contacts:
			ground_normal += contact.normal
		ground_normal = ground_normal.normalized()

		# Calculate surface angle
		surface_angle = rad_to_deg(acos(ground_normal.dot(Vector3.UP)))

		# State transitions
		if was_grounded == false:
			_on_landing()
	else:
		if was_grounded == true:
			_on_takeoff()

func _physics_ground(delta):
	# Get current velocity
	var current_velocity = linear_velocity
	var speed = current_velocity.length()

	# Apply input acceleration
	if input_forward != 0:
		var push_direction = -transform.basis.z
		var push_force = acceleration_force * input_forward

		# Reduce effectiveness at high speeds
		var speed_factor = clamp(1.0 - (speed / max_speed), 0.0, 1.0)
		push_force *= speed_factor

		apply_central_force(push_direction * push_force)

	# Advanced turning physics
	if input_turn != 0 and speed > 0.1:
		# Calculate turn force based on speed (more speed = wider turns)
		var speed_factor = clamp(speed / 10.0, 0.1, 1.0)
		var turn_force = turn_rate * input_turn * speed_factor

		# Apply carving physics
		if abs(input_lean) > 0.1:
			turn_force *= carve_multiplier

			# Add lateral force for realistic carving
			var carve_direction = transform.basis.x * sign(input_turn)
			apply_central_force(carve_direction * abs(input_lean) * speed * 0.5)

		# Apply turning torque
		apply_torque(Vector3.UP * turn_force)

		# Speed wobbles at high speed
		if speed > speed_wobble_threshold:
			var wobble = sin(Time.get_ticks_msec() * 0.01) * speed_wobble_intensity
			apply_torque(Vector3.UP * wobble)

	# Rolling resistance and friction
	var friction_force = -current_velocity.normalized() * rolling_resistance * speed
	friction_force *= surface_properties[current_surface_type].friction
	apply_central_force(friction_force)

	# Bearing friction (speed-dependent)
	linear_velocity *= bearing_friction

	# Brake physics
	if is_braking:
		var brake_direction = -current_velocity.normalized()
		apply_central_force(brake_direction * brake_force)

		# Check for powerslide initiation
		if abs(input_turn) > 0.7 and speed > 5.0:
			_enter_powerslide()

	# Weight distribution affects steering
	var weight_offset = (weight_distribution - 0.5) * 0.2
	center_of_mass = Vector3(0, 0, weight_offset)

func _physics_air(delta):
	# Air control (limited)
	if input_turn != 0:
		var air_turn_rate = turn_rate * 0.3
		apply_torque(Vector3.UP * input_turn * air_turn_rate)

	# Maintain angular momentum
	angular_velocity *= 0.98

	# Air resistance
	var air_drag = -linear_velocity * 0.01
	apply_central_force(air_drag)

	# Trick rotations
	if is_flipping:
		trick_rotation += flip_axis * flip_speed * delta
		rotation = trick_rotation

func _physics_grind(delta):
	# Grind physics would go here
	# Lock to rail, apply grinding friction, handle balance
	pass

func _physics_manual(delta):
	# Manual balance physics
	manual_balance += input_forward * manual_balance_difficulty * delta
	manual_balance = clamp(manual_balance, -1.0, 1.0)

	# Apply rotation based on balance
	var manual_angle = manual_balance * 30.0
	rotation.x = deg_to_rad(manual_angle)

	# Fall out of manual if balance is lost
	if abs(manual_balance) > 0.9:
		_exit_manual()

func _physics_powerslide(delta):
	# Reduced grip during powerslide
	var slide_velocity = linear_velocity
	var forward_component = slide_velocity.dot(-transform.basis.z)
	var lateral_component = slide_velocity.dot(transform.basis.x)

	# Reduce lateral grip
	lateral_component *= powerslide_friction

	# Reconstruct velocity
	linear_velocity = -transform.basis.z * forward_component + transform.basis.x * lateral_component

	# Exit powerslide conditions
	if !is_braking or linear_velocity.length() < 2.0:
		_exit_powerslide()

func _apply_board_dynamics(delta):
	# Board flex simulation
	if is_grounded:
		var target_compression = 0.0

		# Compression from landing or pumping
		if ground_contacts.size() > 0:
			var average_force = linear_velocity.y * -0.1
			target_compression = clamp(average_force, 0.0, landing_compression)

		compression = lerp(compression, target_compression, 10.0 * delta)
	else:
		compression = lerp(compression, 0.0, 5.0 * delta)

	# Lean simulation
	current_lean = lerp(current_lean, input_lean * lean_angle_max, lean_speed * delta)

func _update_visuals(delta):
	if !mesh_instance:
		return

	# Apply compression to mesh
	var compression_scale = 1.0 - compression * 0.1
	mesh_instance.scale.y = compression_scale

	# Apply lean rotation
	mesh_instance.rotation.z = deg_to_rad(current_lean)

	# Truck rotation based on turning
	if front_trucks and back_trucks:
		var truck_angle = input_turn * 15.0 * truck_looseness
		front_trucks.rotation.y = deg_to_rad(truck_angle)
		back_trucks.rotation.y = deg_to_rad(-truck_angle * 0.5)

func _on_landing():
	compression = landing_compression
	current_state = BoardState.GROUND
	state_changed.emit(current_state)

	# Calculate landing quality
	var landing_angle = rad_to_deg(ground_normal.angle_to(Vector3.UP))
	if landing_angle < 15.0:
		# Good landing
		pass
	else:
		# Sketchy landing - add instability
		apply_torque_impulse(Vector3(randf() - 0.5, 0, randf() - 0.5) * 2.0)

func _on_takeoff():
	current_state = BoardState.AIR
	state_changed.emit(current_state)

func _enter_powerslide():
	current_state = BoardState.POWERSLIDE
	state_changed.emit(current_state)

func _exit_powerslide():
	current_state = BoardState.GROUND
	state_changed.emit(current_state)

func _exit_manual():
	current_state = BoardState.GROUND
	state_changed.emit(current_state)

func _on_body_entered(body):
	# Detect rails, ramps, etc.
	if body.is_in_group("rail"):
		_start_grind(body)

func _on_body_exited(body):
	if body.is_in_group("rail") and current_state == BoardState.GRIND:
		_end_grind()

func _start_grind(rail):
	current_state = BoardState.GRIND
	grind_started.emit("rail")
	# Snap to rail logic would go here

func _end_grind():
	var grind_duration = 0.0  # Calculate actual duration
	grind_ended.emit(grind_duration)
	current_state = BoardState.AIR

# Public interface for player controller
func apply_input(forward: float, turn: float, lean: float, brake: bool):
	input_forward = forward
	input_turn = turn
	input_lean = lean
	is_braking = brake

func ollie():
	if current_state == BoardState.GROUND:
		apply_central_impulse(Vector3.UP * ollie_force)
		apply_torque_impulse(Vector3.RIGHT * ollie_torque)
		current_state = BoardState.AIR

func kickflip():
	if current_state == BoardState.AIR and !is_flipping:
		is_flipping = true
		flip_axis = Vector3.RIGHT
		trick_rotation = rotation

func manual():
	if current_state == BoardState.GROUND and linear_velocity.length() > 2.0:
		current_state = BoardState.MANUAL
		manual_balance = 0.0

func set_player(p):
	player = p

func get_speed() -> float:
	return linear_velocity.length()

func get_state() -> BoardState:
	return current_state

func is_on_ground() -> bool:
	return is_grounded
