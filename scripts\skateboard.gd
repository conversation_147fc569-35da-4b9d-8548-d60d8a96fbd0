extends RigidBody3D
class_name Skateboard

# Skateboard physics properties
@export var push_force: float = 15.0
@export var turn_force: float = 3.0
@export var max_speed: float = 20.0
@export var friction_coefficient: float = 0.98
@export var air_resistance: float = 0.02
@export var push_cooldown: float = 0.15
@export var ollie_cooldown: float = 0.5

# Wheel properties
@export var wheel_friction: float = 0.8
@export var wheel_grip: float = 0.9

# EA Skate-like physics
@export var carving_factor: float = 0.7
@export var momentum_preservation: float = 0.95
@export var turn_speed_influence: float = 0.5
@export var push_angle_tolerance: float = 45.0
@export var ollie_force: float = 6.0

# References
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D
@onready var front_wheels: Node3D = $FrontWheels
@onready var back_wheels: Node3D = $BackWheels
@onready var ground_ray: RayCast3D = $GroundRay

# State
var player = null
var push_timer: float = 0.0
var ollie_timer: float = 0.0
var is_grounded: bool = true

# Input tracking (received from player controller)
var turn_input: float = 0.0
var push_input: bool = false
var brake_input: bool = false

signal player_mounted(player_ref)
signal player_dismounted()

func _ready():
	# Add to skateboard group for identification
	add_to_group("skateboard")
	
	# Create skateboard visual
	_create_skateboard_mesh()
	
	# Set physics properties
	mass = 2.0
	gravity_scale = 1.0
	# Prevent the skateboard from tipping over easily
	lock_rotation = false
	# Add some angular damping to prevent excessive spinning
	angular_damp = 2.0
	linear_damp = 0.1

func _create_skateboard_mesh():
	# Skateboard mesh and collision will be set up in the scene editor
	# This function can be removed or used for any runtime setup if needed
	pass



func _physics_process(delta):
	# Update ground check
	if ground_ray:
		is_grounded = ground_ray.is_colliding()
	
	# Update timers
	if ollie_timer > 0:
		ollie_timer -= delta


# New method to receive input from player controller
func handle_player_input(_push: bool, _turn: float, _brake: bool, _delta: float):
	# Movement system removed - will be rebuilt
	pass



# All skateboard physics functions removed

# All movement functions removed - ready for new system

func ollie():
	# A simple ollie by applying an upward impulse.
	if is_grounded and ollie_timer <= 0:
		apply_central_impulse(Vector3.UP * ollie_force)
		ollie_timer = ollie_cooldown

func set_player(new_player):
	if player and new_player == null:
		# Player dismounting
		player = null
		player_dismounted.emit()
	elif new_player and not player:
		# Player mounting
		player = new_player
		player_mounted.emit(player)

# Ground detection will be handled differently
# For now, assume always grounded for simplicity

func get_speed() -> float:
	return linear_velocity.length()

func is_moving() -> bool:
	return linear_velocity.length() > 0.1
