[gd_scene load_steps=22 format=3 uid="uid://dfwqb2pxuyn2x"]

[ext_resource type="Script" uid="uid://cpwlujhr03k32" path="res://scripts/main.gd" id="1_main"]
[ext_resource type="Script" uid="uid://ca6mrkxgqkq8m" path="res://scripts/player_controller.gd" id="2_player"]
[ext_resource type="Script" uid="uid://ci8as5fldyonr" path="res://scripts/camera_controller.gd" id="3_camera"]
[ext_resource type="Script" uid="uid://duopyyl4f35fa" path="res://scripts/level_environment.gd" id="4_environment"]
[ext_resource type="PackedScene" uid="uid://c2mgc2jeoj357" path="res://assets/models/skater/skater.glb" id="5_skater"]
[ext_resource type="PackedScene" uid="uid://b8xqr7y8qn2vp" path="res://scenes/skateboard.tscn" id="6_skateboard"]
[ext_resource type="Material" uid="uid://sxhmstlqtvmt" path="res://addons/kenney_prototype_tools/materials/dark/material_09.tres" id="7_o6xl0"]
[ext_resource type="Material" uid="uid://b8s7ei0hpivs0" path="res://addons/kenney_prototype_tools/materials/red/material_04.tres" id="8_choun"]
[ext_resource type="Material" uid="uid://uylhy3ucrinn" path="res://addons/kenney_prototype_tools/materials/purple/material_04.tres" id="9_ya4ey"]
[ext_resource type="Material" uid="uid://csfdcdo2s8jaw" path="res://addons/kenney_prototype_tools/materials/orange/material_04.tres" id="10_eb6dy"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]
radius = 0.3
height = 1.8

[sub_resource type="BoxShape3D" id="BoxShape3D_feet"]
size = Vector3(0.4, 0.1, 0.2)

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(100, 0.1, 100)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(100, 0.1, 100)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_tefeu"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_3"]

[sub_resource type="BoxShape3D" id="BoxShape3D_2"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5"]
albedo_color = Color(0, 1, 0, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6"]
albedo_color = Color(0, 0, 1, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7"]
albedo_color = Color(1, 1, 0, 1)

[node name="Main" type="Node3D"]
script = ExtResource("1_main")

[node name="Player" type="CharacterBody3D" parent="."]
transform = Transform3D(-1, 0, 8.742278e-08, 0, 1, 0, -8.742278e-08, 0, -1, 0, 1, 0)
script = ExtResource("2_player")
walk_speed = null
run_speed = null
jump_velocity = null
acceleration = null
friction = null
skateboard_scene = ExtResource("6_skateboard")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Player"]

[node name="SkaterModel" parent="Player/MeshInstance3D" instance=ExtResource("5_skater")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.100000024, 0)
shape = SubResource("CapsuleShape3D_1")

[node name="SkateboardPickupArea" type="Area3D" parent="Player"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player/SkateboardPickupArea"]

[node name="DeckDetectionArea" type="Area3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.9, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player/DeckDetectionArea"]
shape = SubResource("BoxShape3D_feet")

[node name="CameraController" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 5)
script = ExtResource("3_camera")

[node name="Camera3D" type="Camera3D" parent="CameraController"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 0, 0)

[node name="CameraPivot" type="Node3D" parent="CameraController"]

[node name="Environment" type="Node3D" parent="."]
script = ExtResource("4_environment")

[node name="Ground" type="StaticBody3D" parent="Environment"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/Ground"]
material_override = ExtResource("7_o6xl0")
mesh = SubResource("BoxMesh_1")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Ground"]
shape = SubResource("BoxShape3D_1")

[node name="Skatepark" type="Node3D" parent="Environment"]

[node name="QuarterPipe1" type="CSGCombiner3D" parent="Environment/Skatepark"]
transform = Transform3D(1, 0, 0, 0, -4.371139e-08, 1, 0, -1, -4.371139e-08, 0, 4.1, -15)
use_collision = true

[node name="CSGBox3D" type="CSGBox3D" parent="Environment/Skatepark/QuarterPipe1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, -3.0999987)
size = Vector3(10, 4, 2)
material = ExtResource("8_choun")

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Environment/Skatepark/QuarterPipe1"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.0168457, 1.9073486e-06)
operation = 2
radius = 4.0
height = 4.0336914
sides = 32
material = SubResource("StandardMaterial3D_tefeu")

[node name="QuarterPipe2" type="CSGCombiner3D" parent="Environment/Skatepark"]
transform = Transform3D(-1, 0, -8.74228e-08, -8.74228e-08, -4.371139e-08, 1, -3.8213718e-15, 1, 4.371139e-08, 0, 3, 15)
use_collision = true

[node name="CSGBox3D" type="CSGBox3D" parent="Environment/Skatepark/QuarterPipe2"]
transform = Transform3D(1, 8.74228e-08, 8.74228e-08, -8.74228e-08, -4.371139e-08, 1, 8.74228e-08, -1, -4.371138e-08, 0, 1, -2)
size = Vector3(10, 2, 4)
material = ExtResource("9_ya4ey")

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="Environment/Skatepark/QuarterPipe2"]
transform = Transform3D(1, 0, -1.6703746e-22, 0, 1, 0, -1.6703746e-22, 0, 1, -8.742265e-08, 0.9046631, 0.99999976)
operation = 2
radius = 4.0
height = 4.2697754
sides = 32
material = ExtResource("9_ya4ey")

[node name="Funbox" type="Node3D" parent="Environment/Skatepark"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 0)

[node name="Ramp1" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 0.9659255, 0.2588192, 0, -0.2588192, 0.9659255, 0, 0.48528597, -4.6888895)
use_collision = true
size = Vector3(4, 1, 7.75)
material = ExtResource("10_eb6dy")

[node name="Box" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.96250004, 0)
use_collision = true
size = Vector3(1, 1.9250001, 0.5)
material = ExtResource("10_eb6dy")

[node name="Ramp2" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 0.9659255, -0.2588192, 0, 0.2588192, 0.9659255, 0, 0.48528597, 4.6888895)
use_collision = true
size = Vector3(4, 1, 7.75)
material = ExtResource("10_eb6dy")

[node name="Rail" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.025, 0)
use_collision = true
size = Vector3(0.2, 0.2, 2.7250001)
material = ExtResource("10_eb6dy")

[node name="CSGBox3D" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5106201, 2.6911035)
use_collision = true
size = Vector3(4, 1.029248, 3.7459183)
material = ExtResource("10_eb6dy")

[node name="CSGBox3D2" type="CSGBox3D" parent="Environment/Skatepark/Funbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5106201, -2.7006426)
use_collision = true
size = Vector3(4, 1.029248, 3.7562866)
material = ExtResource("10_eb6dy")

[node name="ReferenceCubes" type="Node3D" parent="Environment"]

[node name="RedCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0.5, 10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/RedCube"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 10)
material_override = SubResource("StandardMaterial3D_4")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/RedCube"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 10)
shape = SubResource("BoxShape3D_2")

[node name="GreenCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0.5, 10)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/GreenCube"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 10)
material_override = SubResource("StandardMaterial3D_5")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/GreenCube"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 10)
shape = SubResource("BoxShape3D_2")

[node name="BlueCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0.5, -20)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/BlueCube"]
material_override = SubResource("StandardMaterial3D_6")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/BlueCube"]
shape = SubResource("BoxShape3D_2")

[node name="YellowCube" type="StaticBody3D" parent="Environment/ReferenceCubes"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0.5, -20)

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/ReferenceCubes/YellowCube"]
material_override = SubResource("StandardMaterial3D_7")
mesh = SubResource("BoxMesh_3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/ReferenceCubes/YellowCube"]
shape = SubResource("BoxShape3D_2")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 5)
shadow_enabled = true
