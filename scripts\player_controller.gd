extends CharacterBody3D
class_name PlayerController

# Player states
enum PlayerState {
	WALKING,
	SKATEBOARDING,
	BAILING  # New state for falls
}

# Movement properties
@export_group("On Foot Movement")
@export var walk_speed: float = 5.0
@export var run_speed: float = 8.0
@export var jump_velocity: float = 4.5
@export var acceleration: float = 10.0
@export var friction: float = 10.0
@export var air_control: float = 0.3

@export_group("Skateboard Settings")
@export var skateboard_scene: PackedScene
@export var mount_animation_time: float = 0.3
@export var bail_threshold_speed: float = 18.0
@export var bail_threshold_angle: float = 45.0

# State
var current_state: PlayerState = PlayerState.WALKING
var skateboard_instance: RigidBody3D = null
var nearby_skateboard: RigidBody3D = null

# Physics
var gravity: float = ProjectSettings.get_setting("physics/3d/default_gravity")
var is_pushing: bool = false
var push_timer: float = 0.0
var push_cooldown: float = 0.3

# Mounting/Dismounting
var is_mounting: bool = false
var mount_timer: float = 0.0
var mount_start_pos: Vector3
var mount_target_pos: Vector3

# Bailing
var bail_velocity: Vector3 = Vector3.ZERO
var bail_timer: float = 0.0

# Animation States
var current_trick: String = ""
var stance: String = "regular"  # or "goofy"

# References
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D
@onready var skateboard_pickup_area: Area3D = $SkateboardPickupArea
@onready var deck_detection_area: Area3D = $DeckDetectionArea
var camera_controller

# Input buffer for trick detection
var input_buffer: Array = []
var buffer_time: float = 0.3
var last_input_time: float = 0.0

# Collision settings storage for skateboard mode
var original_collision_layer: int
var original_collision_mask: int

# Deck surface detection
var is_on_deck_surface: bool = false

signal state_changed(new_state: PlayerState)
signal trick_performed(trick_name: String)
signal bailed()

func _ready():
	# Store original collision settings
	original_collision_layer = collision_layer
	original_collision_mask = collision_mask

	# Connect area signals
	if skateboard_pickup_area:
		skateboard_pickup_area.body_entered.connect(_on_skateboard_area_entered)
		skateboard_pickup_area.body_exited.connect(_on_skateboard_area_exited)

	if deck_detection_area:
		deck_detection_area.area_entered.connect(_on_deck_area_entered)
		deck_detection_area.area_exited.connect(_on_deck_area_exited)

func _physics_process(delta):
	# Update timers
	if push_timer > 0:
		push_timer -= delta

	# State machine
	match current_state:
		PlayerState.WALKING:
			_handle_walking_physics(delta)
		PlayerState.SKATEBOARDING:
			_handle_skateboard_physics(delta)
		PlayerState.BAILING:
			_handle_bail_physics(delta)

	# Handle mounting animation
	if is_mounting:
		_update_mount_animation(delta)

func _handle_walking_physics(delta):
	# Gravity
	if not is_on_floor():
		velocity.y -= gravity * delta

	# Jump
	if Input.is_action_just_pressed("jump") and is_on_floor():
		velocity.y = jump_velocity

	# Get input
	var input_dir = _get_movement_input()

	# Camera-relative movement
	var direction = _get_camera_relative_direction(input_dir)

	# Apply movement
	if direction != Vector3.ZERO:
		var target_speed = run_speed if Input.is_action_pressed("run") else walk_speed
		velocity.x = move_toward(velocity.x, direction.x * target_speed, acceleration * delta)
		velocity.z = move_toward(velocity.z, direction.z * target_speed, acceleration * delta)

		# Rotate to face direction
		var target_rotation = atan2(direction.x, direction.z)
		rotation.y = lerp_angle(rotation.y, target_rotation, 10.0 * delta)
	else:
		velocity.x = move_toward(velocity.x, 0, friction * delta)
		velocity.z = move_toward(velocity.z, 0, friction * delta)

	move_and_slide()

func _handle_skateboard_physics(delta):
	if not skateboard_instance:
		return

	# Get input
	var input_dir = _get_movement_input()
	var camera_dir = _get_camera_relative_direction(input_dir)

	# Calculate skateboard input
	var forward_input = 0.0
	var turn_input = 0.0
	var lean_input = 0.0

	# Forward/backward from input
	if camera_dir != Vector3.ZERO:
		# Project camera direction onto skateboard's forward/right axes
		var board_forward = -skateboard_instance.transform.basis.z
		var board_right = skateboard_instance.transform.basis.x

		forward_input = camera_dir.dot(board_forward)
		turn_input = camera_dir.dot(board_right)

		# Normalize turn input
		turn_input = clamp(turn_input, -1.0, 1.0)

	# Pushing mechanic (space or push button)
	if Input.is_action_just_pressed("push") and push_timer <= 0:
		forward_input = 1.0
		push_timer = push_cooldown
		is_pushing = true
		trick_performed.emit("push")

	# Braking
	var is_braking = Input.is_action_pressed("brake")

	# Leaning for carves (shift keys or triggers)
	if Input.is_action_pressed("lean_left"):
		lean_input = -1.0
	elif Input.is_action_pressed("lean_right"):
		lean_input = 1.0

	# Send input to skateboard
	if skateboard_instance.has_method("apply_input"):
		skateboard_instance.apply_input(forward_input, turn_input, lean_input, is_braking)

	# Trick detection
	_handle_trick_input(delta)

	# Position player on skateboard
	_update_player_on_skateboard(delta)

	# Check for bail conditions
	_check_bail_conditions()

func _handle_bail_physics(delta):
	# Ragdoll-like physics when bailing
	velocity += Vector3(0, -gravity, 0) * delta
	velocity *= 0.98  # Friction

	# Add some tumbling
	rotation.x += bail_velocity.x * delta
	rotation.z += bail_velocity.z * delta

	move_and_slide()

	# Recover from bail
	bail_timer -= delta
	if bail_timer <= 0 and is_on_floor():
		_recover_from_bail()

func _update_player_on_skateboard(delta):
	if not skateboard_instance:
		return

	# Calculate target position on skateboard
	var board_pos = skateboard_instance.global_position
	var board_rot = skateboard_instance.global_rotation

	# Height offset based on skateboard state
	var height_offset = 0.9
	if skateboard_instance.has_method("get_state"):
		match skateboard_instance.get_state():
			skateboard_instance.BoardState.MANUAL:
				height_offset += 0.1
			skateboard_instance.BoardState.GRIND:
				height_offset += 0.05

	# Target position
	var target_pos = board_pos + Vector3(0, height_offset, 0)

	# Smooth position
	global_position = global_position.lerp(target_pos, 20.0 * delta)

	# Match rotation with style offset for stance
	var style_rotation = board_rot.y
	if stance == "goofy":
		style_rotation += PI

	rotation.y = lerp_angle(rotation.y, style_rotation, 10.0 * delta)

func _handle_trick_input(delta):
	# Detect trick combinations
	_update_input_buffer()

	# Ollie (Jump button)
	if Input.is_action_just_pressed("jump"):
		if skateboard_instance and skateboard_instance.has_method("ollie"):
			skateboard_instance.ollie()
			trick_performed.emit("ollie")
			_add_to_input_buffer("jump")

	# Kickflip (Jump + Kick)
	if Input.is_action_just_pressed("kick") and _is_in_air():
		if skateboard_instance and skateboard_instance.has_method("kickflip"):
			skateboard_instance.kickflip()
			trick_performed.emit("kickflip")

	# Manual (Up/Down balance)
	if Input.is_action_just_pressed("manual") and _is_on_ground():
		if skateboard_instance and skateboard_instance.has_method("manual"):
			skateboard_instance.manual()
			trick_performed.emit("manual")

	# Grabs (in air only)
	if _is_in_air():
		if Input.is_action_pressed("grab_indy"):
			current_trick = "indy"
			trick_performed.emit("indy_grab")
		elif Input.is_action_pressed("grab_melon"):
			current_trick = "melon"
			trick_performed.emit("melon_grab")

func _check_bail_conditions():
	if not skateboard_instance:
		return

	# Check speed threshold
	if skateboard_instance.get_speed() > bail_threshold_speed:
		# Random chance to bail at high speed
		if randf() < 0.001:
			_initiate_bail("speed_wobble")
			return

	# Check landing angle
	if skateboard_instance.has_method("get_landing_angle"):
		var landing_angle = skateboard_instance.get_landing_angle()
		if landing_angle > bail_threshold_angle:
			_initiate_bail("bad_landing")

func _initiate_bail(reason: String):
	if current_state == PlayerState.BAILING:
		return

	current_state = PlayerState.BAILING
	bail_timer = 2.0

	# Calculate bail velocity based on skateboard velocity
	if skateboard_instance:
		bail_velocity = skateboard_instance.linear_velocity * 0.5
		bail_velocity.y = abs(bail_velocity.y) + 2.0  # Add upward component

		# Disconnect from skateboard
		_get_off_skateboard()

	# Apply bail velocity
	velocity = bail_velocity

	# Emit signal
	bailed.emit()
	state_changed.emit(current_state)

func _recover_from_bail():
	current_state = PlayerState.WALKING
	rotation.x = 0
	rotation.z = 0
	state_changed.emit(current_state)

func _update_mount_animation(delta):
	mount_timer += delta
	var t = mount_timer / mount_animation_time

	if t >= 1.0:
		# Animation complete
		is_mounting = false
		global_position = mount_target_pos
	else:
		# Smooth interpolation
		var eased_t = ease(t, -1.5)  # Ease out
		global_position = mount_start_pos.lerp(mount_target_pos, eased_t)

func _get_movement_input() -> Vector2:
	var input = Vector2.ZERO

	if Input.is_action_pressed("move_forward"):
		input.y += 1
	if Input.is_action_pressed("move_backward"):
		input.y -= 1
	if Input.is_action_pressed("move_left"):
		input.x -= 1
	if Input.is_action_pressed("move_right"):
		input.x += 1

	return input.normalized()

func _get_camera_relative_direction(input: Vector2) -> Vector3:
	if not camera_controller:
		return Vector3(input.x, 0, -input.y)

	var cam_transform = camera_controller.global_transform
	var forward = -cam_transform.basis.z
	forward.y = 0
	forward = forward.normalized()

	var right = cam_transform.basis.x
	right.y = 0
	right = right.normalized()

	return forward * input.y + right * input.x

func _is_on_ground() -> bool:
	if skateboard_instance and skateboard_instance.has_method("is_on_ground"):
		return skateboard_instance.is_on_ground()
	return is_on_floor()

func _is_in_air() -> bool:
	return not _is_on_ground()

func _add_to_input_buffer(action: String):
	var current_time = Time.get_ticks_msec() / 1000.0
	input_buffer.append({
		"action": action,
		"time": current_time
	})
	last_input_time = current_time

func _update_input_buffer():
	var current_time = Time.get_ticks_msec() / 1000.0
	input_buffer = input_buffer.filter(func(entry):
		return current_time - entry.time < buffer_time
	)

# Public interface
func _input(event):
	if event.is_action_pressed("interact_skateboard"):
		_toggle_skateboard()

	if event.is_action_pressed("change_stance"):
		stance = "goofy" if stance == "regular" else "regular"

func _toggle_skateboard():
	if current_state == PlayerState.WALKING:
		if nearby_skateboard:
			_get_on_skateboard(nearby_skateboard)
		else:
			_spawn_skateboard()
	else:
		_get_off_skateboard()

func _spawn_skateboard():
	if skateboard_scene:
		skateboard_instance = skateboard_scene.instantiate()
		get_parent().add_child(skateboard_instance)

		# Position skateboard on the ground under player's feet
		# Place skateboard at ground level (y = 0) and let physics handle it
		skateboard_instance.global_position = Vector3(global_position.x, 0.05, global_position.z)

		# Match skateboard rotation to player's current facing direction
		# Add PI/2 + PI to use opposite end as front
		skateboard_instance.rotation.y = rotation.y + PI/2 + PI

		_get_on_skateboard(skateboard_instance)
	else:
		push_error("No skateboard scene assigned!")

func _get_on_skateboard(skateboard: RigidBody3D):
	skateboard_instance = skateboard
	current_state = PlayerState.SKATEBOARDING

	# Start mounting animation
	is_mounting = true
	mount_timer = 0.0
	mount_start_pos = global_position
	mount_target_pos = skateboard.global_position + Vector3(0, 0.9, 0)

	# Connect to skateboard
	if skateboard_instance.has_method("set_player"):
		skateboard_instance.set_player(self)

	state_changed.emit(current_state)

func _get_off_skateboard():
	if skateboard_instance:
		# Disconnect from skateboard
		if skateboard_instance.has_method("set_player"):
			skateboard_instance.set_player(null)

		# Position player next to skateboard
		var dismount_pos = skateboard_instance.global_position + Vector3(1.0, 0, 0)
		global_position = dismount_pos

		# Keep skateboard in world (don't despawn)
		skateboard_instance = null

	current_state = PlayerState.WALKING
	is_mounting = false

	state_changed.emit(current_state)

# Helper function to determine if player should be considered "on floor"
# When on skateboard, we check if standing on deck surface instead of ground
func _is_player_on_floor() -> bool:
	if current_state == PlayerState.SKATEBOARDING:
		return is_on_deck_surface  # When on skateboard, check deck surface
	else:
		return is_on_floor()  # Normal floor detection when walking

func _on_skateboard_area_entered(body):
	if body.is_in_group("skateboard") and not is_on_skateboard:
		nearby_skateboard = body

func _on_skateboard_area_exited(body):
	if body == nearby_skateboard:
		nearby_skateboard = null

func _on_deck_area_entered(area):
	if area.get_parent().is_in_group("skateboard"):
		is_on_deck_surface = true

func _on_deck_area_exited(area):
	if area.get_parent().is_in_group("skateboard"):
		is_on_deck_surface = false

func get_current_state() -> PlayerState:
	return current_state

func is_skateboarding() -> bool:
	return current_state == PlayerState.SKATEBOARDING
