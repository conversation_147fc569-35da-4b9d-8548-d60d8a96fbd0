[gd_scene load_steps=6 format=3 uid="uid://b8xqr7y8qn2vp"]

[ext_resource type="Script" uid="uid://d4mll834qg3wy" path="res://scripts/skateboard.gd" id="1_skateboard"]
[ext_resource type="PackedScene" uid="uid://2eo5xg3tty2a" path="res://assets/models/skateboard/skateboard.glb" id="2_skateboard_model"]

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(1.8, 0.15, 0.5)

[sub_resource type="BoxShape3D" id="BoxShape3D_deck"]
size = Vector3(1.15, 0.047999997, 0.42999998)

[sub_resource type="BoxShape3D" id="BoxShape3D_ma3gn"]
size = Vector3(0.20339844, 0.04, 0.42999998)

[node name="Skateboard" type="RigidBody3D" groups=["skateboard"]]
mass = 2.0
continuous_cd = true
contact_monitor = true
max_contacts_reported = 10
linear_damp = 0.5
angular_damp = 1.0
script = ExtResource("1_skateboard")
ollie_force = 12.0

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]

[node name="SkateboardModel" parent="MeshInstance3D" instance=ExtResource("2_skateboard_model")]
transform = Transform3D(0.175, 0, 0, 0, 0.175, 0, 0, 0, 0.175, 0, 0.037733465, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.075, 0)
shape = SubResource("BoxShape3D_1")

[node name="FrontWheels" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0.4)

[node name="DeckSurface" type="Area3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="DeckSurface"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0016113222, 0.13601111, 0.00026614964)
shape = SubResource("BoxShape3D_deck")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="DeckSurface"]
transform = Transform3D(0.93969274, -0.34202018, 0, 0.34202018, 0.93969274, 0, 0, 0, 1, 0.6610395, 0.16906922, 0.00026614964)
shape = SubResource("BoxShape3D_ma3gn")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="DeckSurface"]
transform = Transform3D(-0.9063076, -0.42261824, 0, 0.42261824, -0.9063076, 0, 0, 0, 1, -0.65727, 0.17269444, 0.00026614964)
shape = SubResource("BoxShape3D_ma3gn")

[node name="BackWheels" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, -0.4)

[node name="GroundRay" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.15236163, 0)
target_position = Vector3(0, -0.25, 0)

[node name="GroundRayFrontLeft" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.3, 0.15, 0.4)
target_position = Vector3(0, -0.5, 0)
enabled = true

[node name="GroundRayFrontRight" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.3, 0.15, 0.4)
target_position = Vector3(0, -0.5, 0)
enabled = true

[node name="GroundRayBackLeft" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.3, 0.15, -0.4)
target_position = Vector3(0, -0.5, 0)
enabled = true

[node name="GroundRayBackRight" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.3, 0.15, -0.4)
target_position = Vector3(0, -0.5, 0)
enabled = true
