[gd_scene load_steps=5 format=3 uid="uid://b8xqr7y8qn2vp"]

[ext_resource type="Script" uid="uid://d4mll834qg3wy" path="res://scripts/skateboard.gd" id="1_skateboard"]
[ext_resource type="PackedScene" uid="uid://2eo5xg3tty2a" path="res://assets/models/skateboard/skateboard.glb" id="2_skateboard_model"]

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(2, 0.05341492, 0.5)

[sub_resource type="BoxShape3D" id="BoxShape3D_deck"]
size = Vector3(1.5305665, 0.029189453, 0.45599976)

[node name="Skateboard" type="RigidBody3D" groups=["skateboard"]]
mass = 2.0
script = ExtResource("1_skateboard")
ollie_force = 8.5

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]

[node name="SkateboardModel" parent="MeshInstance3D" instance=ExtResource("2_skateboard_model")]
transform = Transform3D(0.175, 0, 0, 0, 0.175, 0, 0, 0, 0.175, 0, 0.037733465, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.025741324, 0)
shape = SubResource("BoxShape3D_1")

[node name="FrontWheels" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0.4)

[node name="DeckSurface" type="Area3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="DeckSurface"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0016113222, 0.14541638, 0.013266042)
shape = SubResource("BoxShape3D_deck")

[node name="BackWheels" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, -0.4)

[node name="GroundRay" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.15236163, 0)
target_position = Vector3(0, -0.25, 0)
